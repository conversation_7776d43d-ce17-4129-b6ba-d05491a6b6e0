<template>
  <div style="position: relative;">
    <ccv-line-chart ref="lineChart" :data="chartData" :options="mergedOptions"></ccv-line-chart>
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { CvButton, CvTextInput } from '@carbon/vue'; // Import Carbon Components

Vue.use(chartsVue);
Vue.component('cv-button', CvButton);
Vue.component('cv-text-input', CvTextInput);

export default {
  name: 'PerformanceChart',
  props: {
    trackingItem: {
      type: Object,
      required: true
    },
    loading: {
      type: Boolean,
      default: true
    },
    height: {
      type: String,
      default: "400px"
    },
    eventType: {
      type: Function,
      default: () => {}
    },
    title: {
      type: String,
      default: 'Performance vs Target'
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      chartData: [],
      showActionBox: false,
      actionInput: '',
      actionBoxStyle: {
        position: 'absolute',
        left: '0px',
        top: '0px',
        display: 'none'
      },
      defaultOptions: {
        title: `${this.title}`,
        axes: {
          bottom: {
            title: 'Date',
            mapsTo: 'date',
            scaleType: 'time'
          },
          left: {
            title: 'Performance (%)',
            mapsTo: 'value',
            scaleType: 'linear',
            domain: [60, 100], // Set fixed range for better visualization
            includeZero: false
          }
        },
        curve: 'curveMonotoneX',
        height: this.height,
        theme: 'g90',
        color: {
          scale: {
            'Actual Performance': '#0f62fe',
            'Target': '#42be65'
          }
        },
        points: {
          enabled: true,
          radius: 3,
          filled: true
        },
        grid: {
          x: {enabled: true},
          y: {enabled: true}
        },
        legend: {
          enabled: true,
          position: 'bottom',
          clickable: true
        },
        toolbar: {
          enabled: true
        },
        animations: {
          enabled: true,
          duration: 500
        },
        data: {
          loading: this.loading
        },
        tooltip: {
          enabled: true,
          customHTML: (data) => {
            return `
              <div style="padding: 10px; background: #262626; border: 1px solid #444;">
                <div style="font-weight: bold; margin-bottom: 5px;">${data[0].date}</div>
                <div style="color: ${data[0].group === 'Actual Performance' ? '#0f62fe' : '#42be65'}">
                  ${data[0].group}: ${data[0].value}%
                </div>
              </div>
            `;
          }
        }
      }
    };
  },
  computed: {
    mergedOptions() {
      // Merge the default options with the props options
      const mergedOptions = { ...this.defaultOptions };

      // If props.options is provided, merge it with the default options
      if (this.$props.options) {
        // Deep merge for nested properties
        Object.keys(this.$props.options).forEach(key => {
          if (typeof this.$props.options[key] === 'object' && !Array.isArray(this.$props.options[key])) {
            mergedOptions[key] = {
              ...(mergedOptions[key] || {}),
              ...this.$props.options[key]
            };

            // Special handling for axes to ensure thresholds are properly merged
            if (key === 'axes' && this.$props.options.axes) {
              Object.keys(this.$props.options.axes).forEach(axisKey => {
                if (typeof this.$props.options.axes[axisKey] === 'object') {
                  mergedOptions.axes[axisKey] = {
                    ...(mergedOptions.axes[axisKey] || {}),
                    ...this.$props.options.axes[axisKey]
                  };
                }
              });
            }
          } else {
            mergedOptions[key] = this.$props.options[key];
          }
        });
      }

      // Update loading state
      if (mergedOptions.data) {
        mergedOptions.data.loading = this.loading;
      }

      return mergedOptions;
    }
  },
  watch: {
    trackingItem: {
      immediate: true,
      handler() {
        this.loadPerformanceData();
      }
    }
  },
  mounted() {
  },
  methods: {
    async loadPerformanceData() {
      try {
        console.log('Loading performance data for:', this.trackingItem.group);

        // Generate sample performance data for demonstration
        // In a real implementation, this would fetch actual performance data from the API
        const sampleData = this.generateSamplePerformanceData();
        this.chartData = sampleData;
        console.log("Performance data loaded:", this.chartData);

      } catch (error) {
        console.error('Error loading performance data:', error);
        this.chartData = [];
      }
    },

    // Generate sample performance data
    // generateSamplePerformanceData() {
    //   const data = [];
    //   const today = new Date();
    //   const target = 95; // Target performance percentage
      
    //   // Get expected improvements from tracking item if available
    //   let expectedImprovement = 0;
    //   if (this.trackingItem && this.trackingItem.expectedImprovements) {
    //     // Try to extract percentage from expectedImprovements string
    //     const percentMatch = this.trackingItem.expectedImprovements.match(/(\d+)%/);
    //     if (percentMatch && percentMatch[1]) {
    //       expectedImprovement = parseInt(percentMatch[1], 10);
    //     }
    //   }
      
    //   // Use progress from tracking item if available
    //   const progress = this.trackingItem && this.trackingItem.progress ? this.trackingItem.progress : 50;
      
    //   // Calculate start and improvement trajectory based on item status
    //   let startPerformance = 70; // Default starting point
    //   let endPerformance = 95;   // Default target
      
    //   if (this.trackingItem) {
    //     // Adjust based on status
    //     if (this.trackingItem.status === 'Resolved') {
    //       startPerformance = 75;
    //       endPerformance = 95 + (expectedImprovement / 10);
    //     } else if (this.trackingItem.status === 'Monitored') {
    //       startPerformance = 85;
    //       endPerformance = 90 + (expectedImprovement / 20);
    //     } else if (this.trackingItem.status === 'In-Progress') {
    //       startPerformance = 70;
    //       endPerformance = 80 + (progress / 5);
    //     }
    //   }

    //   // Generate 30 days of sample data
    //   for (let i = 29; i >= 0; i--) {
    //     const date = new Date(today);
    //     date.setDate(date.getDate() - i);
        
    //     // Calculate performance based on day (improving over time)
    //     const progressFactor = 1 - (i / 30); // 0 to 1 as we get closer to today
    //     const basePerformance = startPerformance + (endPerformance - startPerformance) * progressFactor;
        
    //     // Add some variation but ensure overall trend follows the progress
    //     const variation = (Math.random() - 0.5) * 5; // +/- 2.5%
    //     const actualPerformance = Math.max(60, Math.min(100, basePerformance + variation));

    //     data.push({
    //       group: 'Actual Performance',
    //       date: date.toISOString().split('T')[0],
    //       value: Math.round(actualPerformance * 10) / 10 // Round to 1 decimal place
    //     });

    //     data.push({
    //       group: 'Target',
    //       date: date.toISOString().split('T')[0],
    //       value: target
    //     });
    //   }

    //   return data;
    // },

    // Refresh chart data
    refreshData() {
      this.loadPerformanceData();
    }
  }
};
</script>

<style scoped>
/* Filled style */
.action-box {
  background-color: rgb(66, 66, 66);
  border: 1px solid #ff3a3a;
  padding: 16px;
  border-radius: 4px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
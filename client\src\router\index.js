import Vue from 'vue'
import VueRouter from 'vue-router'
// import LoginPage from '../views/LoginPage'
import LoginPage from '../views/LoginPage'
import HomePage from '../views/HomePage'
import SystemLevelFPY from '../views/SystemLevelFPY'
import CommodityOverview from '../views/CommodityOverview'
import PNAnalysis from '../views/PNAnalysis'
import Phase1 from '../views/Phase1'
import SavedReports from '../views/SavedReports'
import ChartPage from '../views/ChartPage'
import ActionTracker from '../views/ActionTracker'
import DefectValidations from '../views/DefectValidations'
import Validation2 from '../views/Validation2/Validation2'
import WatsonX from '../views/WatsonX'
import XFactorOverall from '../views/XFactorOverall'
import PartGroupAnalysis from '../views/PartGroupAnalysis/PartGroupAnalysis'
import MetisXFactors from '../views/MetisXFactors'
import PQEDashboardPage from '../views/PQEDashboard/PQEDashboardPage'
// import CreateChecklistPage from '../views/CreateChecklistPage'

import store from '@/store'
// import TeamPage from '@/views/TeamPage/TeamPage' // <-- aliased path
// import EmployeeCombine from '@/views/HomePage/EmployeeCombine'
// import ManagerCombine from '@/views/ManagerHome/ManagerCombine'
// import DeveloperPage from '@/views/DeveloperHome/DeveloperPage'
// import CertifierCombine from '@/views/CertifierHome/CertifierCombine'
// import TrainerCombine from '@/views/TrainerHome/TrainerCombine'
// import CertificationPage from '@/views/CertificationPage/CertificationPage' // <-- aliased path
// import AdminPage from '@/views/AdminPage/AdminPage' // <-- aliased path
// import ViewCertificationPage from '@/views/ViewCertificationPage/ViewCertificationPage' // <-- aliased path
// import CertAreaOwner from '@/views/CertAreaOwner/CertAreaOwnerCombine'
// import NoPermission from '@/views/NoPermissions/NoPermissionsPage'


Vue.use(VueRouter)


//change order of routes to view what comes first
const routes = [

  {
    path: '/',
    name: 'login-page',
    component: LoginPage,
    meta: {
      requiresAuth: false
    }
  },
  {
    path: '/home-page',
    name: 'HomePage',
    component: HomePage,
  },
  {
    path: '/commodity-overview',
    name: 'CommodityOverview',
    component: CommodityOverview,
  },
  {
    path: '/system-level-fpy',
    name: 'SystemLevelFPY',
    component: SystemLevelFPY,
  },
  {
    path: '/pn-analysis',
    name: 'PNAnalysis',
    component: PNAnalysis,
  },
  {
    path: '/phase1',
    name: 'Phase1',
    component: Phase1,
  },
  {
    path: '/saved-reports',
    name: 'SavedReports',
    component: SavedReports,
  },
  {
    path: '/action-tracker',
    name: 'ActionTracker',
    component: ActionTracker,
  },
  {
    path: '/validations',
    name: 'DefectValidations',
    component: DefectValidations,
  },
  {
    path: '/validation2',
    name: 'ValidationAiPage',
    component: Validation2,
  },
  {
    path: '/watsonX',
    name: 'WatsonX',
    component: WatsonX,
  },
  {
    path: '/XFactorOverall',
    name: 'XFactorOverall',
    component: XFactorOverall
  },

  {
    path: '/x-factor-analysis',
    name: 'PartGroupAnalysis',
    component: PartGroupAnalysis
  },

  {
    path: '/metis-xfactors',
    name: 'MetisXFactors',
    component: MetisXFactors
  },

  {
    path: '/chart',
    name: 'chart-page',
    component: ChartPage,
    meta: {
      requiresAuth: false
    }
  },

  {
    path: '/pqe-dashboard',
    name: 'PQEDashboardPage',
    component: PQEDashboardPage
  },

]

const router = new VueRouter({
  mode: 'history',
  routes
})

if (localStorage.getItem('rememberMe') === 'true') {
  router.push('/home').then((r) => console.log(r))
}

router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters.getAuth

  console.log(isAuthenticated)
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    if (!isAuthenticated) {
      next('/')
    } else {
      next()
    }
  } else {
    next()
  }
})

export default router

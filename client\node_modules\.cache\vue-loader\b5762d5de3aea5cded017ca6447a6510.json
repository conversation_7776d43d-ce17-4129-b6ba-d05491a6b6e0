{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756311176776}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Phase1.vue"], "names": [], "mappings": ";AAiNA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "Phase1.vue", "sourceRoot": "src/views/Phase1", "sourcesContent": ["<template>\n  <cv-grid class=\"phase1-grid\">\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" @panel-toggle=\"toggleSideNav\" />\n    \n    <cv-row class=\"page-header\">\n      <cv-column>\n        <h1 class=\"page-title\">Phase 1 Analysis</h1>\n        <p class=\"page-description\">Advanced data analysis with flexible querying capabilities</p>\n      </cv-column>\n    </cv-row>\n\n    <!-- Control Panel -->\n    <cv-row class=\"control-panel\">\n      <cv-column>\n        <cv-tile class=\"controls-tile\">\n          <h4 class=\"controls-title\">Analysis Controls</h4>\n          \n          <!-- Query Method Selection -->\n          <div class=\"control-section\">\n            <label class=\"control-label\">Query Method:</label>\n            <cv-radio-group \n              v-model=\"queryMethod\" \n              @change=\"handleQueryMethodChange\"\n              class=\"query-method-group\"\n            >\n              <cv-radio-button \n                name=\"query-method\" \n                label=\"Dropdown Builder\" \n                value=\"dropdown\"\n              />\n              <cv-radio-button \n                name=\"query-method\" \n                label=\"AI Prompt\" \n                value=\"ai\"\n              />\n            </cv-radio-group>\n          </div>\n\n          <!-- Dropdown Query Builder -->\n          <div v-if=\"queryMethod === 'dropdown'\" class=\"dropdown-builder\">\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">View By:</label>\n                <cv-dropdown\n                  v-model=\"viewBy\"\n                  @change=\"handleViewByChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                  <cv-dropdown-item value=\"partNum\">Part Number</cv-dropdown-item>\n                  <cv-dropdown-item value=\"category\">Category</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"timeRange\"\n                  @change=\"handleTimeRangeChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"1month\">1 Month</cv-dropdown-item>\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"12month\">12 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"custom\">Custom Range</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\" v-if=\"timeRange === 'custom'\">\n                <label class=\"filter-label\">Date Range:</label>\n                <cv-date-picker\n                  v-model=\"customDateRange\"\n                  label=\"Select Date Range\"\n                  kind=\"range\"\n                  :cal-options=\"calOptions\"\n                  placeholder=\"yyyy-mm-dd\"\n                  @change=\"handleDateRangeChange\"\n                />\n              </div>\n            </div>\n\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Process:</label>\n                <cv-dropdown\n                  v-model=\"selectedProcess\"\n                  @change=\"handleProcessChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Processes</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FAB\">FAB</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FUL\">FUL</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Part Group:</label>\n                <cv-dropdown\n                  v-model=\"selectedPartGroup\"\n                  @change=\"handlePartGroupChange\"\n                  class=\"filter-dropdown\"\n                  :disabled=\"!partGroupOptions.length\"\n                >\n                  <cv-dropdown-item value=\"all\">All Part Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in partGroupOptions\"\n                    :key=\"group.value\"\n                    :value=\"group.value\"\n                  >\n                    {{ group.label }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <cv-button \n                  @click=\"executeDropdownQuery\"\n                  :disabled=\"isLoading\"\n                  class=\"execute-button\"\n                >\n                  {{ isLoading ? 'Loading...' : 'Execute Query' }}\n                </cv-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- AI Prompt Interface -->\n          <div v-if=\"queryMethod === 'ai'\" class=\"ai-prompt-section\">\n            <div class=\"prompt-input-group\">\n              <label class=\"control-label\">Natural Language Query:</label>\n              <cv-text-area\n                v-model=\"aiPrompt\"\n                placeholder=\"Example: Show me the top 5 root causes for FAB process in the last 3 months with failure rates above 2%\"\n                rows=\"4\"\n                class=\"prompt-textarea\"\n              />\n            </div>\n            <div class=\"prompt-controls\">\n              <cv-button \n                @click=\"executeAiQuery\"\n                :disabled=\"isLoading || !aiPrompt.trim()\"\n                class=\"execute-button\"\n              >\n                {{ isLoading ? 'Processing...' : 'Ask AI' }}\n              </cv-button>\n              <cv-button \n                kind=\"secondary\"\n                @click=\"clearAiPrompt\"\n                class=\"clear-button\"\n              >\n                Clear\n              </cv-button>\n            </div>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n\n    <!-- Results Section -->\n    <cv-row class=\"results-section\" v-if=\"hasResults\">\n      <cv-column>\n        <cv-tile class=\"results-tile\">\n          <h4 class=\"results-title\">{{ resultsTitle }}</h4>\n          \n          <!-- Loading State -->\n          <div v-if=\"isLoading\" class=\"loading-section\">\n            <cv-loading />\n            <p>Processing your query...</p>\n          </div>\n\n          <!-- Chart Display -->\n          <div v-else-if=\"chartData.length > 0\" class=\"chart-section\">\n            <BarChart \n              v-if=\"chartType === 'bar'\"\n              :data=\"chartData\" \n              @bar-clicked=\"handleBarClick\" \n              :loading=\"isLoading\"\n            />\n            <LineChart \n              v-if=\"chartType === 'line'\"\n              :data=\"chartData\" \n              @point-clicked=\"handlePointClick\" \n              :loading=\"isLoading\"\n            />\n          </div>\n\n          <!-- AI Response Display -->\n          <div v-if=\"aiResponse\" class=\"ai-response-section\">\n            <h5>AI Analysis:</h5>\n            <div class=\"ai-response-content\">\n              {{ aiResponse }}\n            </div>\n          </div>\n\n          <!-- No Results Message -->\n          <div v-else-if=\"!isLoading\" class=\"no-results\">\n            <p>No data found for the current query. Try adjusting your filters or prompt.</p>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n  </cv-grid>\n</template>\n\n<script>\nimport MainHeader from '../../components/MainHeader';\nimport BarChart from '../../components/BarChart';\nimport LineChart from '../../components/LineChart';\n\nexport default {\n  name: 'Phase1Page',\n  components: {\n    MainHeader,\n    BarChart,\n    LineChart\n  },\n  data() {\n    return {\n      // UI State\n      expandedSideNav: false,\n      useFixed: true,\n      isLoading: false,\n      hasResults: false,\n\n      // Query Method\n      queryMethod: 'dropdown',\n\n      // Dropdown Query Builder\n      viewBy: 'rootCause',\n      timeRange: '3month',\n      customDateRange: '',\n      selectedProcess: 'all',\n      selectedPartGroup: 'all',\n      partGroupOptions: [],\n\n      // AI Prompt\n      aiPrompt: '',\n      aiResponse: '',\n\n      // Results\n      chartData: [],\n      chartType: 'bar',\n      resultsTitle: '',\n\n      // Options\n      calOptions: { dateFormat: \"Y-m-d\" }\n    };\n  },\n  mounted() {\n    this.loadPartGroupOptions();\n  },\n  methods: {\n    toggleSideNav() {\n      this.expandedSideNav = !this.expandedSideNav;\n    },\n\n    handleQueryMethodChange() {\n      this.clearResults();\n    },\n\n    handleViewByChange() {\n      // Auto-execute if we have enough parameters\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleTimeRangeChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleDateRangeChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleProcessChange() {\n      this.loadPartGroupOptions();\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handlePartGroupChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    async loadPartGroupOptions() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch('/api-statit2/get_phase1_part_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({ \n            process: this.selectedProcess \n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.partGroupOptions = data.part_groups || [];\n        }\n      } catch (error) {\n        console.error('Error loading part group options:', error);\n      }\n    },\n\n    async executeDropdownQuery() {\n      if (this.isLoading) return;\n\n      this.isLoading = true;\n      this.hasResults = true;\n\n      try {\n        const token = this.$store.getters.getToken;\n        const queryParams = {\n          viewBy: this.viewBy,\n          timeRange: this.timeRange,\n          customDateRange: this.timeRange === 'custom' ? this.customDateRange : null,\n          process: this.selectedProcess,\n          partGroup: this.selectedPartGroup\n        };\n\n        const response = await fetch('/api-statit2/execute_phase1_dropdown_query', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify(queryParams),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.chartData = data.chart_data || [];\n          this.chartType = data.chart_type || 'bar';\n          this.resultsTitle = data.title || 'Analysis Results';\n          this.aiResponse = '';\n        }\n      } catch (error) {\n        console.error('Error executing dropdown query:', error);\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async executeAiQuery() {\n      if (this.isLoading || !this.aiPrompt.trim()) return;\n\n      this.isLoading = true;\n      this.hasResults = true;\n\n      try {\n        const token = this.$store.getters.getToken;\n        \n        const response = await fetch('/api-statit2/execute_phase1_ai_query', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({ \n            prompt: this.aiPrompt.trim()\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.chartData = data.chart_data || [];\n          this.chartType = data.chart_type || 'bar';\n          this.resultsTitle = data.title || 'AI Analysis Results';\n          this.aiResponse = data.ai_response || '';\n        }\n      } catch (error) {\n        console.error('Error executing AI query:', error);\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    clearAiPrompt() {\n      this.aiPrompt = '';\n      this.clearResults();\n    },\n\n    clearResults() {\n      this.chartData = [];\n      this.aiResponse = '';\n      this.hasResults = false;\n      this.resultsTitle = '';\n    },\n\n    handleResponse(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Handle session expiration\n          this.$router.push('/');\n        }\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      return response.json();\n    },\n\n    handleBarClick(data) {\n      console.log('Bar clicked:', data);\n      // Handle bar chart interactions\n    },\n\n    handlePointClick(data) {\n      console.log('Point clicked:', data);\n      // Handle line chart interactions\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../styles/carbon-utils\";\n\n.phase1-grid {\n  min-height: 100vh;\n  background-color: $ui-background;\n}\n\n.page-header {\n  margin-top: $spacing-07;\n  margin-bottom: $spacing-05;\n}\n\n.page-title {\n  @include carbon--type-style('productive-heading-04');\n  color: $text-01;\n  margin-bottom: $spacing-03;\n}\n\n.page-description {\n  @include carbon--type-style('body-long-01');\n  color: $text-02;\n}\n\n.control-panel {\n  margin-bottom: $spacing-05;\n}\n\n.controls-tile {\n  padding: $spacing-05;\n}\n\n.controls-title {\n  @include carbon--type-style('productive-heading-02');\n  color: $text-01;\n  margin-bottom: $spacing-05;\n}\n\n.control-section {\n  margin-bottom: $spacing-05;\n}\n\n.control-label {\n  @include carbon--type-style('label-01');\n  color: $text-01;\n  display: block;\n  margin-bottom: $spacing-03;\n}\n\n.query-method-group {\n  display: flex;\n  gap: $spacing-05;\n}\n\n.dropdown-builder {\n  .filter-row {\n    display: flex;\n    gap: $spacing-05;\n    margin-bottom: $spacing-04;\n    flex-wrap: wrap;\n  }\n\n  .filter-group {\n    flex: 1;\n    min-width: 200px;\n  }\n\n  .filter-label {\n    @include carbon--type-style('label-01');\n    color: $text-01;\n    display: block;\n    margin-bottom: $spacing-02;\n  }\n\n  .filter-dropdown {\n    width: 100%;\n  }\n}\n\n.ai-prompt-section {\n  .prompt-input-group {\n    margin-bottom: $spacing-04;\n  }\n\n  .prompt-textarea {\n    width: 100%;\n  }\n\n  .prompt-controls {\n    display: flex;\n    gap: $spacing-03;\n  }\n}\n\n.execute-button {\n  min-width: 120px;\n}\n\n.clear-button {\n  min-width: 80px;\n}\n\n.results-section {\n  margin-bottom: $spacing-07;\n}\n\n.results-tile {\n  padding: $spacing-05;\n}\n\n.results-title {\n  @include carbon--type-style('productive-heading-02');\n  color: $text-01;\n  margin-bottom: $spacing-05;\n}\n\n.loading-section {\n  text-align: center;\n  padding: $spacing-07;\n  \n  p {\n    @include carbon--type-style('body-long-01');\n    color: $text-02;\n    margin-top: $spacing-03;\n  }\n}\n\n.chart-section {\n  min-height: 400px;\n}\n\n.ai-response-section {\n  margin-top: $spacing-05;\n  padding: $spacing-04;\n  background-color: $ui-01;\n  border-radius: $spacing-02;\n\n  h5 {\n    @include carbon--type-style('productive-heading-01');\n    color: $text-01;\n    margin-bottom: $spacing-03;\n  }\n}\n\n.ai-response-content {\n  @include carbon--type-style('body-long-01');\n  color: $text-01;\n  line-height: 1.6;\n  white-space: pre-wrap;\n}\n\n.no-results {\n  text-align: center;\n  padding: $spacing-07;\n  \n  p {\n    @include carbon--type-style('body-long-01');\n    color: $text-02;\n  }\n}\n</style>\n"]}]}
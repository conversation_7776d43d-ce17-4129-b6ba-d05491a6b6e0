{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756311702622}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Phase1.vue"], "names": [], "mappings": ";AA+QA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Phase1.vue", "sourceRoot": "src/views/Phase1", "sourcesContent": ["<template>\n  <cv-grid class=\"phase1-grid\">\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" @panel-toggle=\"toggleSideNav\" />\n    \n    <cv-row class=\"page-header\">\n      <cv-column>\n        <h1 class=\"page-title\">Phase 1 Analysis</h1>\n        <p class=\"page-description\">Advanced data analysis with flexible querying capabilities</p>\n      </cv-column>\n    </cv-row>\n\n    <!-- Control Panel -->\n    <cv-row class=\"control-panel\">\n      <cv-column :lg=\"8\" :md=\"6\" :sm=\"4\">\n        <cv-tile class=\"controls-tile\">\n          <div class=\"controls-header\">\n            <h4 class=\"controls-title\">Analysis Controls</h4>\n\n            <!-- Query Method Selection -->\n            <cv-radio-group\n              v-model=\"queryMethod\"\n              @change=\"handleQueryMethodChange\"\n              class=\"query-method-group\"\n            >\n              <cv-radio-button\n                name=\"query-method\"\n                label=\"Dropdown Builder\"\n                value=\"dropdown\"\n              />\n              <cv-radio-button\n                name=\"query-method\"\n                label=\"AI Prompt\"\n                value=\"ai\"\n              />\n            </cv-radio-group>\n          </div>\n\n          <!-- Dropdown Query Builder -->\n          <div v-show=\"queryMethod === 'dropdown'\" class=\"dropdown-builder\">\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">View By:</label>\n                <cv-dropdown\n                  v-model=\"viewBy\"\n                  @change=\"handleViewByChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                  <cv-dropdown-item value=\"partNum\">Part Number</cv-dropdown-item>\n                  <cv-dropdown-item value=\"category\">Category</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"timeRange\"\n                  @change=\"handleTimeRangeChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"1month\">1 Month</cv-dropdown-item>\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"12month\">12 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"custom\">Custom Range</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Process:</label>\n                <cv-dropdown\n                  v-model=\"selectedProcess\"\n                  @change=\"handleProcessChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Processes</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FAB\">FAB</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FUL\">FUL</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Part Group:</label>\n                <cv-dropdown\n                  v-model=\"selectedPartGroup\"\n                  @change=\"handlePartGroupChange\"\n                  class=\"filter-dropdown\"\n                  :disabled=\"!partGroupOptions.length\"\n                >\n                  <cv-dropdown-item value=\"all\">All Part Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in partGroupOptions\"\n                    :key=\"group.value\"\n                    :value=\"group.value\"\n                  >\n                    {{ group.label }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\" v-if=\"timeRange === 'custom'\">\n                <label class=\"filter-label\">Date Range:</label>\n                <cv-date-picker\n                  v-model=\"customDateRange\"\n                  label=\"Select Date Range\"\n                  kind=\"range\"\n                  :cal-options=\"calOptions\"\n                  placeholder=\"yyyy-mm-dd\"\n                  @change=\"handleDateRangeChange\"\n                />\n              </div>\n\n              <div class=\"filter-group action-group\">\n                <cv-button\n                  @click=\"executeDropdownQuery\"\n                  :disabled=\"isLoading\"\n                  class=\"execute-button\"\n                >\n                  {{ isLoading ? 'Loading...' : 'Execute Query' }}\n                </cv-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- AI Prompt Interface -->\n          <div v-show=\"queryMethod === 'ai'\" class=\"ai-prompt-section\">\n            <div class=\"prompt-input-group\">\n              <label class=\"control-label\">Natural Language Query:</label>\n              <cv-text-area\n                v-model=\"aiPrompt\"\n                placeholder=\"Example: Show me the top 5 root causes for FAB process in the last 3 months with failure rates above 2%\"\n                rows=\"3\"\n                class=\"prompt-textarea\"\n              />\n            </div>\n            <div class=\"prompt-controls\">\n              <cv-button\n                @click=\"executeAiQuery\"\n                :disabled=\"isLoading || !aiPrompt.trim()\"\n                class=\"execute-button\"\n              >\n                {{ isLoading ? 'Processing...' : 'Ask AI' }}\n              </cv-button>\n              <cv-button\n                kind=\"secondary\"\n                @click=\"clearAiPrompt\"\n                class=\"clear-button\"\n              >\n                Clear\n              </cv-button>\n            </div>\n          </div>\n        </cv-tile>\n      </cv-column>\n\n      <!-- Action Panel -->\n      <cv-column :lg=\"4\" :md=\"2\" :sm=\"4\" v-if=\"hasResults\">\n        <cv-tile class=\"action-tile\">\n          <h5 class=\"action-title\">Actions</h5>\n          <div class=\"action-buttons\">\n            <cv-button\n              @click=\"saveJob\"\n              :disabled=\"isLoading\"\n              class=\"action-button\"\n              kind=\"secondary\"\n            >\n              Save Job\n            </cv-button>\n            <cv-button\n              @click=\"exportData('csv')\"\n              :disabled=\"isLoading || !chartData.length\"\n              class=\"action-button\"\n              kind=\"tertiary\"\n            >\n              Export CSV\n            </cv-button>\n            <cv-button\n              @click=\"exportData('pdf')\"\n              :disabled=\"isLoading || !chartData.length\"\n              class=\"action-button\"\n              kind=\"tertiary\"\n            >\n              Export PDF\n            </cv-button>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n\n    <!-- Results Section -->\n    <cv-row class=\"results-section\" v-if=\"hasResults\">\n      <cv-column>\n        <cv-tile class=\"results-tile\">\n          <div class=\"results-header\">\n            <h4 class=\"results-title\">{{ resultsTitle }}</h4>\n            <div class=\"results-meta\" v-if=\"!isLoading && chartData.length > 0\">\n              <span class=\"data-count\">{{ chartData.length }} data points</span>\n              <span class=\"query-time\">{{ queryExecutionTime }}</span>\n            </div>\n          </div>\n\n          <!-- Loading State -->\n          <div v-if=\"isLoading\" class=\"loading-section\">\n            <cv-loading />\n            <p>Processing your query...</p>\n          </div>\n\n          <!-- Chart Display -->\n          <div v-else-if=\"chartData.length > 0\" class=\"chart-section\">\n            <div class=\"chart-container\">\n              <BarChart\n                v-if=\"chartType === 'bar'\"\n                :data=\"chartData\"\n                @bar-clicked=\"handleBarClick\"\n                :loading=\"false\"\n                class=\"chart-component\"\n              />\n              <LineChart\n                v-if=\"chartType === 'line'\"\n                :data=\"chartData\"\n                @point-clicked=\"handlePointClick\"\n                :loading=\"false\"\n                class=\"chart-component\"\n              />\n            </div>\n\n            <!-- Data Table -->\n            <div class=\"data-table-section\">\n              <h5>Data Summary</h5>\n              <cv-data-table\n                :columns=\"tableColumns\"\n                :data=\"tableData\"\n                :pagination=\"{ numberOfItems: chartData.length }\"\n                class=\"results-table\"\n              >\n                <template v-slot:cell=\"{ cell }\">\n                  <div v-if=\"cell.header === 'value'\" class=\"value-cell\">\n                    {{ formatValue(cell.value) }}\n                  </div>\n                  <div v-else>\n                    {{ cell.value }}\n                  </div>\n                </template>\n              </cv-data-table>\n            </div>\n          </div>\n\n          <!-- AI Response Display -->\n          <div v-if=\"aiResponse\" class=\"ai-response-section\">\n            <h5>AI Analysis:</h5>\n            <div class=\"ai-response-content\">\n              {{ aiResponse }}\n            </div>\n          </div>\n\n          <!-- No Results Message -->\n          <div v-else-if=\"!isLoading\" class=\"no-results\">\n            <p>No data found for the current query. Try adjusting your filters or prompt.</p>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n  </cv-grid>\n</template>\n\n<script>\nimport MainHeader from '../../components/MainHeader';\nimport BarChart from '../../components/BarChart';\nimport LineChart from '../../components/LineChart';\n\nexport default {\n  name: 'Phase1Page',\n  components: {\n    MainHeader,\n    BarChart,\n    LineChart\n  },\n  data() {\n    return {\n      // UI State\n      expandedSideNav: false,\n      useFixed: true,\n      isLoading: false,\n      hasResults: false,\n\n      // Query Method\n      queryMethod: 'dropdown',\n\n      // Dropdown Query Builder\n      viewBy: 'rootCause',\n      timeRange: '3month',\n      customDateRange: '',\n      selectedProcess: 'all',\n      selectedPartGroup: 'all',\n      partGroupOptions: [],\n\n      // AI Prompt\n      aiPrompt: '',\n      aiResponse: '',\n\n      // Results\n      chartData: [],\n      chartType: 'bar',\n      resultsTitle: '',\n      queryExecutionTime: '',\n\n      // Table Data\n      tableColumns: [\n        { header: 'Category', key: 'group' },\n        { header: 'Value', key: 'value' },\n        { header: 'Details', key: 'details' }\n      ],\n\n      // Current Job Data\n      currentJobData: null,\n\n      // Options\n      calOptions: { dateFormat: \"Y-m-d\" }\n    };\n  },\n  computed: {\n    tableData() {\n      return this.chartData.map((item, index) => ({\n        id: index,\n        group: item.group,\n        value: item.value,\n        details: item.details || 'N/A'\n      }));\n    }\n  },\n  mounted() {\n    this.loadPartGroupOptions();\n  },\n  methods: {\n    toggleSideNav() {\n      this.expandedSideNav = !this.expandedSideNav;\n    },\n\n    handleQueryMethodChange() {\n      this.clearResults();\n    },\n\n    handleViewByChange() {\n      // Auto-execute if we have enough parameters\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleTimeRangeChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleDateRangeChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleProcessChange() {\n      this.loadPartGroupOptions();\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handlePartGroupChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    async loadPartGroupOptions() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch('/api-statit2/get_phase1_part_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({ \n            process: this.selectedProcess \n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.partGroupOptions = data.part_groups || [];\n        }\n      } catch (error) {\n        console.error('Error loading part group options:', error);\n      }\n    },\n\n    async executeDropdownQuery() {\n      if (this.isLoading) return;\n\n      this.isLoading = true;\n      this.hasResults = true;\n      const startTime = Date.now();\n\n      try {\n        const token = this.$store.getters.getToken;\n        const queryParams = {\n          viewBy: this.viewBy,\n          timeRange: this.timeRange,\n          customDateRange: this.timeRange === 'custom' ? this.customDateRange : null,\n          process: this.selectedProcess,\n          partGroup: this.selectedPartGroup\n        };\n\n        const response = await fetch('/api-statit2/execute_phase1_dropdown_query', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify(queryParams),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.chartData = data.chart_data || [];\n          this.chartType = data.chart_type || 'bar';\n          this.resultsTitle = data.title || 'Analysis Results';\n          this.aiResponse = '';\n\n          // Store current job data\n          this.currentJobData = {\n            type: 'dropdown',\n            params: queryParams,\n            results: data,\n            timestamp: new Date().toISOString()\n          };\n        }\n      } catch (error) {\n        console.error('Error executing dropdown query:', error);\n      } finally {\n        this.isLoading = false;\n        const endTime = Date.now();\n        this.queryExecutionTime = `Executed in ${endTime - startTime}ms`;\n      }\n    },\n\n    async executeAiQuery() {\n      if (this.isLoading || !this.aiPrompt.trim()) return;\n\n      this.isLoading = true;\n      this.hasResults = true;\n      const startTime = Date.now();\n\n      try {\n        const token = this.$store.getters.getToken;\n\n        const response = await fetch('/api-statit2/execute_phase1_ai_query', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({\n            prompt: this.aiPrompt.trim()\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.chartData = data.chart_data || [];\n          this.chartType = data.chart_type || 'bar';\n          this.resultsTitle = data.title || 'AI Analysis Results';\n          this.aiResponse = data.ai_response || '';\n\n          // Store current job data\n          this.currentJobData = {\n            type: 'ai',\n            params: { prompt: this.aiPrompt.trim() },\n            results: data,\n            timestamp: new Date().toISOString()\n          };\n        }\n      } catch (error) {\n        console.error('Error executing AI query:', error);\n      } finally {\n        this.isLoading = false;\n        const endTime = Date.now();\n        this.queryExecutionTime = `Executed in ${endTime - startTime}ms`;\n      }\n    },\n\n    clearAiPrompt() {\n      this.aiPrompt = '';\n      this.clearResults();\n    },\n\n    clearResults() {\n      this.chartData = [];\n      this.aiResponse = '';\n      this.hasResults = false;\n      this.resultsTitle = '';\n    },\n\n    handleResponse(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Handle session expiration\n          this.$router.push('/');\n        }\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      return response.json();\n    },\n\n    handleBarClick(data) {\n      console.log('Bar clicked:', data);\n      // Handle bar chart interactions\n    },\n\n    handlePointClick(data) {\n      console.log('Point clicked:', data);\n      // Handle line chart interactions\n    },\n\n    formatValue(value) {\n      if (typeof value === 'number') {\n        return value.toLocaleString();\n      }\n      return value;\n    },\n\n    async exportData(format) {\n      if (!this.chartData.length) return;\n\n      try {\n        const token = this.$store.getters.getToken;\n        const exportData = {\n          format: format,\n          data: this.chartData,\n          title: this.resultsTitle,\n          queryParams: this.currentJobData?.params || {},\n          timestamp: new Date().toISOString()\n        };\n\n        const response = await fetch('/api-statit2/export_phase1_data', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify(exportData),\n        });\n\n        if (response.ok) {\n          const blob = await response.blob();\n          const url = window.URL.createObjectURL(blob);\n          const a = document.createElement('a');\n          a.href = url;\n          a.download = `phase1_analysis_${Date.now()}.${format}`;\n          document.body.appendChild(a);\n          a.click();\n          window.URL.revokeObjectURL(url);\n          document.body.removeChild(a);\n        }\n      } catch (error) {\n        console.error('Error exporting data:', error);\n      }\n    },\n\n    async saveJob() {\n      if (!this.currentJobData) return;\n\n      try {\n        const token = this.$store.getters.getToken;\n        const jobName = prompt('Enter a name for this job:');\n        if (!jobName) return;\n\n        const saveData = {\n          name: jobName,\n          jobData: this.currentJobData,\n          createdAt: new Date().toISOString()\n        };\n\n        const response = await fetch('/api-statit2/save_phase1_job', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify(saveData),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          alert(`Job \"${jobName}\" saved successfully!`);\n          // Navigate to saved reports tab\n          this.$router.push('/saved-reports');\n        }\n      } catch (error) {\n        console.error('Error saving job:', error);\n        alert('Error saving job. Please try again.');\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../styles/carbon-utils\";\n\n.phase1-grid {\n  min-height: 100vh;\n  background-color: $ui-background;\n  padding: 0 $spacing-05;\n}\n\n.page-header {\n  margin-top: $spacing-05;\n  margin-bottom: $spacing-04;\n}\n\n.page-title {\n  @include carbon--type-style('productive-heading-03');\n  color: $text-01;\n  margin-bottom: $spacing-02;\n}\n\n.page-description {\n  @include carbon--type-style('body-short-01');\n  color: $text-02;\n}\n\n.control-panel {\n  margin-bottom: $spacing-04;\n}\n\n.controls-tile {\n  padding: $spacing-04;\n  height: fit-content;\n}\n\n.controls-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $spacing-04;\n}\n\n.controls-title {\n  @include carbon--type-style('productive-heading-01');\n  color: $text-01;\n  margin: 0;\n}\n\n.control-label {\n  @include carbon--type-style('label-01');\n  color: $text-01;\n  display: block;\n  margin-bottom: $spacing-02;\n}\n\n.query-method-group {\n  display: flex;\n  gap: $spacing-04;\n}\n\n.action-tile {\n  padding: $spacing-04;\n  height: fit-content;\n}\n\n.action-title {\n  @include carbon--type-style('productive-heading-01');\n  color: $text-01;\n  margin-bottom: $spacing-03;\n}\n\n.action-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: $spacing-03;\n}\n\n.action-button {\n  width: 100%;\n}\n\n.dropdown-builder {\n  .filter-row {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: $spacing-04;\n    margin-bottom: $spacing-03;\n  }\n\n  .filter-group {\n    min-width: 0;\n\n    &.action-group {\n      display: flex;\n      align-items: end;\n    }\n  }\n\n  .filter-label {\n    @include carbon--type-style('label-01');\n    color: $text-01;\n    display: block;\n    margin-bottom: $spacing-02;\n  }\n\n  .filter-dropdown {\n    width: 100%;\n  }\n}\n\n.ai-prompt-section {\n  .prompt-input-group {\n    margin-bottom: $spacing-03;\n  }\n\n  .prompt-textarea {\n    width: 100%;\n  }\n\n  .prompt-controls {\n    display: flex;\n    gap: $spacing-03;\n  }\n}\n\n.execute-button {\n  min-width: 120px;\n}\n\n.clear-button {\n  min-width: 80px;\n}\n\n.results-section {\n  margin-bottom: $spacing-05;\n}\n\n.results-tile {\n  padding: $spacing-04;\n}\n\n.results-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $spacing-04;\n}\n\n.results-title {\n  @include carbon--type-style('productive-heading-02');\n  color: $text-01;\n  margin: 0;\n}\n\n.results-meta {\n  display: flex;\n  gap: $spacing-04;\n\n  .data-count, .query-time {\n    @include carbon--type-style('caption-01');\n    color: $text-02;\n  }\n}\n\n.loading-section {\n  text-align: center;\n  padding: $spacing-05;\n\n  p {\n    @include carbon--type-style('body-short-01');\n    color: $text-02;\n    margin-top: $spacing-03;\n  }\n}\n\n.chart-section {\n  .chart-container {\n    height: 350px;\n    margin-bottom: $spacing-05;\n  }\n\n  .chart-component {\n    height: 100%;\n  }\n}\n\n.data-table-section {\n  h5 {\n    @include carbon--type-style('productive-heading-01');\n    color: $text-01;\n    margin-bottom: $spacing-03;\n  }\n\n  .results-table {\n    max-height: 300px;\n    overflow-y: auto;\n  }\n\n  .value-cell {\n    font-weight: 600;\n    color: $text-01;\n  }\n}\n\n.ai-response-section {\n  margin-top: $spacing-04;\n  padding: $spacing-04;\n  background-color: $ui-01;\n  border-radius: $spacing-02;\n\n  h5 {\n    @include carbon--type-style('productive-heading-01');\n    color: $text-01;\n    margin-bottom: $spacing-03;\n  }\n}\n\n.ai-response-content {\n  @include carbon--type-style('body-short-01');\n  color: $text-01;\n  line-height: 1.5;\n  white-space: pre-wrap;\n}\n\n.no-results {\n  text-align: center;\n  padding: $spacing-05;\n\n  p {\n    @include carbon--type-style('body-short-01');\n    color: $text-02;\n  }\n}\n</style>\n"]}]}
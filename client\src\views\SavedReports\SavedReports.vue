<template>
  <cv-grid class="saved-reports-grid">
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" @panel-toggle="toggleSideNav" />
    
    <cv-row class="page-header">
      <cv-column>
        <h1 class="page-title">Saved Reports</h1>
        <p class="page-description">Manage and schedule your saved analysis reports</p>
      </cv-column>
    </cv-row>

    <!-- Reports Management -->
    <cv-row class="reports-section">
      <cv-column>
        <cv-tile class="reports-tile">
          <div class="reports-header">
            <h4 class="reports-title">Saved Analysis Jobs</h4>
            <cv-button @click="refreshReports" kind="secondary" class="refresh-button">
              Refresh
            </cv-button>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading" class="loading-section">
            <cv-loading />
            <p>Loading saved reports...</p>
          </div>

          <!-- Reports Table -->
          <div v-else-if="savedReports.length > 0" class="reports-table-section">
            <cv-data-table
              :columns="reportsColumns"
              :data="savedReports"
              :pagination="{ numberOfItems: savedReports.length }"
              class="reports-table"
            >
              <template v-slot:cell="{ cell, row }">
                <div v-if="cell.header === 'actions'" class="actions-cell">
                  <cv-button 
                    @click="viewReport(row)"
                    kind="ghost"
                    size="sm"
                    class="action-btn"
                  >
                    View
                  </cv-button>
                  <cv-button 
                    @click="scheduleReport(row)"
                    kind="ghost"
                    size="sm"
                    class="action-btn"
                  >
                    Schedule
                  </cv-button>
                  <cv-button 
                    @click="deleteReport(row)"
                    kind="danger--ghost"
                    size="sm"
                    class="action-btn"
                  >
                    Delete
                  </cv-button>
                </div>
                <div v-else-if="cell.header === 'status'" class="status-cell">
                  <cv-tag 
                    :type="getStatusType(cell.value)"
                    :label="cell.value"
                  />
                </div>
                <div v-else>
                  {{ cell.value }}
                </div>
              </template>
            </cv-data-table>
          </div>

          <!-- No Reports Message -->
          <div v-else class="no-reports">
            <p>No saved reports found. Create reports from the Phase 1 analysis page.</p>
            <cv-button @click="goToPhase1" class="create-button">
              Go to Phase 1 Analysis
            </cv-button>
          </div>
        </cv-tile>
      </cv-column>
    </cv-row>

    <!-- Scheduling Modal -->
    <cv-modal 
      v-model="scheduleModalVisible"
      :primary-button-disabled="!selectedSchedule"
      @primary-click="saveSchedule"
      @secondary-click="closeScheduleModal"
    >
      <template v-slot:label>Schedule Report</template>
      <template v-slot:title>{{ selectedReport.name }}</template>
      <template v-slot:content>
        <div class="schedule-form">
          <div class="form-group">
            <label class="form-label">Schedule Type:</label>
            <cv-radio-group v-model="selectedSchedule" class="schedule-options">
              <cv-radio-button 
                name="schedule-type" 
                label="Daily" 
                value="daily"
              />
              <cv-radio-button 
                name="schedule-type" 
                label="Weekly" 
                value="weekly"
              />
              <cv-radio-button 
                name="schedule-type" 
                label="Monthly" 
                value="monthly"
              />
            </cv-radio-group>
          </div>

          <div class="form-group" v-if="selectedSchedule === 'weekly'">
            <label class="form-label">Day of Week:</label>
            <cv-dropdown v-model="scheduleDay" class="schedule-dropdown">
              <cv-dropdown-item value="monday">Monday</cv-dropdown-item>
              <cv-dropdown-item value="tuesday">Tuesday</cv-dropdown-item>
              <cv-dropdown-item value="wednesday">Wednesday</cv-dropdown-item>
              <cv-dropdown-item value="thursday">Thursday</cv-dropdown-item>
              <cv-dropdown-item value="friday">Friday</cv-dropdown-item>
            </cv-dropdown>
          </div>

          <div class="form-group" v-if="selectedSchedule === 'monthly'">
            <label class="form-label">Day of Month:</label>
            <cv-number-input 
              v-model="scheduleDay" 
              :min="1" 
              :max="28"
              label="Day"
            />
          </div>

          <div class="form-group">
            <label class="form-label">Time:</label>
            <cv-text-input 
              v-model="scheduleTime" 
              placeholder="HH:MM (24-hour format)"
              pattern="^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
            />
          </div>

          <div class="form-group">
            <label class="form-label">Email Recipients:</label>
            <cv-text-area 
              v-model="emailRecipients"
              placeholder="Enter email addresses separated by commas"
              rows="3"
            />
          </div>
        </div>
      </template>
      <template v-slot:primary-button>Save Schedule</template>
      <template v-slot:secondary-button>Cancel</template>
    </cv-modal>

    <!-- View Report Modal -->
    <cv-modal 
      v-model="viewModalVisible"
      size="lg"
      @secondary-click="closeViewModal"
    >
      <template v-slot:label>Saved Report</template>
      <template v-slot:title>{{ selectedReport && selectedReport.name }}</template>
      <template v-slot:content>
        <div class="report-details" v-if="selectedReport">
          <div class="report-meta">
            <p><strong>Created:</strong> {{ formatDate(selectedReport.createdAt) }}</p>
            <p><strong>Query Type:</strong> {{ (selectedReport.data && selectedReport.data.type) || 'Unknown' }}</p>
            <p><strong>Parameters:</strong></p>
            <pre class="params-display">{{ JSON.stringify((selectedReport.data && selectedReport.data.params) || {}, null, 2) }}</pre>
          </div>

          <div class="report-results" v-if="selectedReport.data && selectedReport.data.results">
            <h5>Results Summary</h5>
            <p><strong>Title:</strong> {{ selectedReport.data.results.title }}</p>
            <p><strong>Data Points:</strong> {{ (selectedReport.data.results.chart_data && selectedReport.data.results.chart_data.length) || 0 }}</p>

            <div class="chart-preview" v-if="selectedReport.data.results.chart_data && selectedReport.data.results.chart_data.length > 0">
              <h6>Data Preview</h6>
              <div class="data-preview">
                <div
                  v-for="(item, index) in selectedReport.data.results.chart_data.slice(0, 5)"
                  :key="index"
                  class="data-item"
                >
                  <span class="data-label">{{ item.group }}:</span>
                  <span class="data-value">{{ item.value }}</span>
                </div>
                <div v-if="selectedReport.data.results.chart_data.length > 5" class="more-data">
                  ... and {{ selectedReport.data.results.chart_data.length - 5 }} more items
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-slot:secondary-button>Close</template>
    </cv-modal>
  </cv-grid>
</template>

<script>
import MainHeader from '../../components/MainHeader';

export default {
  name: 'SavedReports',
  components: {
    MainHeader
  },
  data() {
    return {
      // UI State
      expandedSideNav: false,
      useFixed: true,
      isLoading: false,

      // Reports Data
      savedReports: [],
      reportsColumns: [
        { header: 'Name', key: 'name' },
        { header: 'Created', key: 'createdAt' },
        { header: 'Type', key: 'type' },
        { header: 'Status', key: 'status' },
        { header: 'Actions', key: 'actions' }
      ],

      // Modal States
      scheduleModalVisible: false,
      viewModalVisible: false,
      selectedReport: null,

      // Scheduling Form
      selectedSchedule: '',
      scheduleDay: '',
      scheduleTime: '09:00',
      emailRecipients: ''
    };
  },
  mounted() {
    this.loadSavedReports();
  },
  methods: {
    toggleSideNav: function() {
      this.expandedSideNav = !this.expandedSideNav;
    },

    loadSavedReports: function() {
      var self = this;
      self.isLoading = true;

      var token = self.$store.getters.getToken;
      fetch('/api-statit2/get_saved_reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
        body: JSON.stringify({}),
      })
      .then(function(response) {
        return self.handleResponse(response);
      })
      .then(function(data) {
        if (data && data.status_res === 'success') {
          self.savedReports = data.reports.map(function(report) {
            return {
              id: report.id,
              name: report.name,
              createdAt: self.formatDate(report.createdAt),
              type: (report.data && report.data.type === 'ai') ? 'AI Query' : 'Dropdown Query',
              status: report.scheduled ? 'Scheduled' : 'Saved',
              data: report.data
            };
          });
        }
      })
      .catch(function(error) {
        console.error('Error loading saved reports:', error);
      })
      .finally(function() {
        self.isLoading = false;
      });
    },

    refreshReports: function() {
      this.loadSavedReports();
    },

    viewReport: function(report) {
      this.selectedReport = report;
      this.viewModalVisible = true;
    },

    scheduleReport: function(report) {
      this.selectedReport = report;
      this.selectedSchedule = '';
      this.scheduleDay = '';
      this.scheduleTime = '09:00';
      this.emailRecipients = '';
      this.scheduleModalVisible = true;
    },

    deleteReport: function(report) {
      var self = this;
      if (!confirm('Are you sure you want to delete "' + report.name + '"?')) return;

      var token = self.$store.getters.getToken;
      fetch('/api-statit2/delete_saved_report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
        body: JSON.stringify({ reportId: report.id }),
      })
      .then(function(response) {
        return self.handleResponse(response);
      })
      .then(function(data) {
        if (data && data.status_res === 'success') {
          self.loadSavedReports();
        }
      })
      .catch(function(error) {
        console.error('Error deleting report:', error);
      });
    },

    saveSchedule: function() {
      var self = this;
      var token = self.$store.getters.getToken;

      var scheduleData = {
        reportId: self.selectedReport.id,
        scheduleType: self.selectedSchedule,
        scheduleDay: self.scheduleDay,
        scheduleTime: self.scheduleTime,
        emailRecipients: self.emailRecipients.split(',').map(function(email) {
          return email.trim();
        }).filter(function(email) {
          return email;
        })
      };

      fetch('/api-statit2/schedule_report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
        body: JSON.stringify(scheduleData),
      })
      .then(function(response) {
        return self.handleResponse(response);
      })
      .then(function(data) {
        if (data && data.status_res === 'success') {
          self.closeScheduleModal();
          self.loadSavedReports();
          alert('Report scheduled successfully!');
        }
      })
      .catch(function(error) {
        console.error('Error scheduling report:', error);
        alert('Error scheduling report. Please try again.');
      });
    },

    closeScheduleModal: function() {
      this.scheduleModalVisible = false;
      this.selectedReport = null;
    },

    closeViewModal: function() {
      this.viewModalVisible = false;
      this.selectedReport = null;
    },

    goToPhase1: function() {
      this.$router.push('/phase1');
    },

    getStatusType: function(status) {
      switch (status) {
        case 'Scheduled': return 'blue';
        case 'Saved': return 'green';
        default: return 'gray';
      }
    },

    formatDate: function(dateString) {
      if (!dateString) return 'N/A';
      return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();
    },

    handleResponse: function(response) {
      if (!response.ok) {
        if (response.status === 401) {
          this.$router.push('/');
        }
        throw new Error('HTTP error! status: ' + response.status);
      }
      return response.json();
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../styles/carbon-utils";

.saved-reports-grid {
  min-height: 100vh;
  background-color: #161616;
  padding: 0 $spacing-05;
}

.page-header {
  margin-top: $spacing-05;
  margin-bottom: $spacing-04;
}

.page-title {
  @include carbon--type-style('productive-heading-03');
  color: $text-01;
  margin-bottom: $spacing-02;
}

.page-description {
  @include carbon--type-style('body-short-01');
  color: $text-02;
}

.reports-section {
  margin-bottom: $spacing-05;
}

.reports-tile {
  padding: $spacing-05;
  background-color: #262626;
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-05;
}

.reports-title {
  @include carbon--type-style('productive-heading-02');
  color: $text-01;
  margin: 0;
}

.loading-section {
  text-align: center;
  padding: $spacing-07;
  
  p {
    @include carbon--type-style('body-short-01');
    color: $text-02;
    margin-top: $spacing-03;
  }
}

.reports-table-section {
  .reports-table {
    margin-bottom: $spacing-05;
  }

  .actions-cell {
    display: flex;
    gap: $spacing-02;
  }

  .action-btn {
    min-width: 60px;
  }

  .status-cell {
    display: flex;
    align-items: center;
  }
}

.no-reports {
  text-align: center;
  padding: $spacing-07;
  
  p {
    @include carbon--type-style('body-long-01');
    color: $text-02;
    margin-bottom: $spacing-05;
  }
}

.schedule-form {
  .form-group {
    margin-bottom: $spacing-05;
  }

  .form-label {
    @include carbon--type-style('label-01');
    color: $text-01;
    display: block;
    margin-bottom: $spacing-03;
  }

  .schedule-options {
    display: flex;
    gap: $spacing-05;
  }

  .schedule-dropdown {
    width: 100%;
  }
}

.report-details {
  .report-meta {
    margin-bottom: $spacing-05;
    
    p {
      @include carbon--type-style('body-short-01');
      color: $text-01;
      margin-bottom: $spacing-02;
    }

    .params-display {
      background-color: $ui-01;
      padding: $spacing-03;
      border-radius: $spacing-02;
      font-family: 'IBM Plex Mono', monospace;
      font-size: 12px;
      overflow-x: auto;
    }
  }

  .report-results {
    h5, h6 {
      @include carbon--type-style('productive-heading-01');
      color: $text-01;
      margin-bottom: $spacing-03;
    }

    .chart-preview {
      margin-top: $spacing-04;
      
      .data-preview {
        background-color: $ui-01;
        padding: $spacing-04;
        border-radius: $spacing-02;
        
        .data-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: $spacing-02;
          
          .data-label {
            font-weight: 600;
          }
        }

        .more-data {
          @include carbon--type-style('caption-01');
          color: $text-02;
          font-style: italic;
          margin-top: $spacing-02;
        }
      }
    }
  }
}
</style>

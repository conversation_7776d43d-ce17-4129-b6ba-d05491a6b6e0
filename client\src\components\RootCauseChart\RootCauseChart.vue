

<template>
  <div ref='chartHolder'>
  </div>
</template>

<script>
import Vue from 'vue';
import '@carbon/charts/styles.css'; // Import Carbon Charts styles
import chartsVue from '@carbon/charts-vue';
import { StackedBarChart } from '@carbon/charts'; // Import StackedBarChart

Vue.use(chartsVue);

export default {
  name: 'RootCauseChart',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: true // Default is loading true
    },
    title: {
      type: String,
      default: ''
    },
    eventType: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      options:{
      title: this.title ,
        axes: {
          left: {
            title: 'Fail Rate (%)',
            mapsTo: 'value',
            stacked: true,
            
          },
          bottom: {
            title: 'Month',
            mapsTo: 'key',
            scaleType: 'labels'
          }
        },
        legend: {
          alignment: 'center',
          enabled: true
        },
        toolbar: {
          enabled: true
        },
        tooltip: {
          enabled: true
        },
        height: this.height,
        theme: 'g100',
        stacked: true,
        animations: true,
        bars: {
          width: 50
        },
        data: {
          groupMapsTo: 'group',
          loading: this.loading
        }
      }
    }
    },
  mounted() {

    const chartHolder = this.$refs.chartHolder;
    console.log(chartHolder);

    const colorScale = this.generateColorScale(this.data);

    this.options.color = { scale: colorScale };

    this.chart = new StackedBarChart(chartHolder, {
      data: this.data,
      options: this.options,
    });

    this.chart.services.events.addEventListener('bar-click', (e) => {
      this.eventType(e);
    });
  },
  methods: {
  generateColorScale(data) {
  const colorMap = {};
  const groups = Array.from(new Set(data.map(d => d.group)));
  //const numGroups = groups.length;

  // Shuffle hue values using a golden angle for better distribution
  const goldenAngle = 137.508; // in degrees

  groups.forEach((group, index) => {
    if (group === 'NEEDS VALIDATION') {
      colorMap[group] = '#808080'; // fixed grey
    } else {
      // Use golden angle to separate colors more distinctly on hue circle
      const hue = (index * goldenAngle) % 360;

      // Optional: small random variation in saturation/lightness to help distinguish close hues
      const saturation = 65 + (index % 2) * 10; // alternate between 65% and 75%
      const lightness = 55 + ((index + 1) % 3) * 5; // alternate between 55%, 60%, 65%

      colorMap[group] = `hsl(${hue.toFixed(1)}, ${saturation}%, ${lightness}%)`;
    }
  });

  return colorMap;
}


},
  watch: {
    loading(newVal) {
      // Update loading state dynamically when the prop changes
      this.options.data.loading = newVal;
      this.chart.model.setOptions(this.options);
    },
    data(newData) {
        this.chart.model.setData(newData);
      
    }
  },
};
</script>

<style>
button[aria-label="Show as table"] {
  display: none;
  pointer-events: none;
}
div.toolbar-control.bx--overflow-menu[aria-label="Show as table"] {
  display: none;
}
</style>

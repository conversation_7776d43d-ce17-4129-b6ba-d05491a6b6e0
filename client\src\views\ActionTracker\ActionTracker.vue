<style scoped lang="scss">
@import "../../styles/carbon-utils";

.dashboard-container {
  min-height: 100vh;
  background-color: #161616;
}

.main-content {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #333333;
}

.page-title {
  color: #f4f4f4;
  font-size: 1.75rem;
  font-weight: 400;
  margin: 0;
}

.action-controls {
  display: flex;
  gap: 1rem;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  background-color: #262626;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #333333;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-label {
  color: #8d8d8d;
  font-size: 0.875rem;
}

.search-box {
  flex-grow: 1;
  max-width: 300px;
}

/* Table styling */
.action-table {
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.table-header {
  background-color: #333333;
  padding: 1rem;
  border-bottom: 1px solid #444444;
}

.table-title {
  color: #f4f4f4;
  font-size: 1.25rem;
  font-weight: 400;
  margin: 0;
}

/* Cell styling */
.edit-icon {
  margin-left: 8px;
  cursor: pointer;
  color: #0f62fe;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.edit-icon:hover {
  opacity: 1;
}

.editable-field {
  display: flex;
  align-items: center;
}

.editable-field input {
  background-color: #333333;
  border: 1px solid #0f62fe;
  color: #f4f4f4;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.action-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-text {
  flex-grow: 1;
}

.see-more-button {
  background-color: #0f62fe;
  color: #ffffff;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;
}

.see-more-button:hover {
  background-color: #0353e9;
}

/* Status indicators */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status-in-progress {
  background-color: #0f62fe;
}

.status-completed {
  background-color: #42be65;
}

.status-blocked {
  background-color: #fa4d56;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.in-progress {
  background-color: rgba(15, 98, 254, 0.2);
  color: #78a9ff;
}

.status-badge.completed {
  background-color: rgba(66, 190, 101, 0.2);
  color: #6fdc8c;
}

.status-badge.blocked {
  background-color: rgba(250, 77, 86, 0.2);
  color: #ff8389;
}

/* Status banner */
.status-banner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.status-banner-in-progress {
  background-color: rgba(15, 98, 254, 0.1);
  border: 1px solid rgba(15, 98, 254, 0.3);
}

.status-banner-completed {
  background-color: rgba(66, 190, 101, 0.1);
  border: 1px solid rgba(66, 190, 101, 0.3);
}

.status-banner-blocked {
  background-color: rgba(250, 77, 86, 0.1);
  border: 1px solid rgba(250, 77, 86, 0.3);
}

.assignee-info {
  color: #f4f4f4;
  font-size: 0.875rem;
}

/* Modal actions */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

/* Form styling */
.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.form-group.full-width {
  grid-column: span 2;
}

.form-label {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-bottom: 0.5rem;
}



.modal-content {
  padding: 1.5rem 0;
  color: #f4f4f4;
}

.modal-section {
  margin-bottom: 2rem;
}

.section-title {
  font-weight: 600;
  margin-bottom: 1rem;
  font-size: 1.125rem;
  color: #f4f4f4;
  border-bottom: 1px solid #333333;
  padding-bottom: 0.5rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-bottom: 0.375rem;
}

.info-value {
  font-size: 1rem;
  color: #f4f4f4;
}

.action-details {
  background-color: #333333;
  padding: 1.5rem;
  border-radius: 8px;
  margin-top: 1rem;
  border: 1px solid #444444;
}

.action-title {
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #f4f4f4;
  font-size: 1rem;
}

.action-description {
  white-space: pre-line;
  margin-bottom: 1.5rem;
  color: #f4f4f4;
  line-height: 1.5;
}

/* Required field indicator */
.required-field {
  color: #fa4d56;
  margin-left: 4px;
  font-weight: bold;
}

/* Form validation styles */
.form-error {
  color: #fa4d56;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Form note */
.form-note {
  color: #8d8d8d;
  font-size: 0.875rem;
  margin-bottom: 1rem;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .info-grid {
    grid-template-columns: 1fr;
  }

  .filter-bar {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .filter-group {
    width: 100%;
    justify-content: space-between;
  }

  .search-box {
    max-width: 100%;
  }
}

@media (max-width: 768px) {
  .main-content {
    padding: 1.5rem 1rem;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .action-controls {
    width: 100%;
  }
}

/* Loading and error states */
.loading-indicator, .loading-message {
  color: #0f62fe;
  font-size: 14px;
  margin-top: 10px;
}

.error-message {
  color: #da1e28;
  font-size: 14px;
  margin-top: 10px;
}

.empty-message {
  color: #8d8d8d;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Progress bar styling */
.progress-container {
  width: 100%;
  padding: 0.5rem 0;
}

.detail-progress-bar {
  margin-bottom: 1rem;
}

.progress-update-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  background-color: #333333;
  padding: 1rem;
  border-radius: 4px;
}

/* Updates styling */
.updates-list {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.update-item {
  background-color: #333333;
  padding: 1rem;
  border-radius: 4px;
  border-left: 3px solid #0f62fe;
}

.update-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #8d8d8d;
}

.update-content {
  color: #f4f4f4;
  line-height: 1.5;
}

.update-date {
  font-weight: 600;
}

.update-by {
  font-style: italic;
}

.add-update-form {
  margin-top: 1rem;
}

.update-form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.expected-improvements {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Progress indicator in table */
.cv-progress {
  width: 100%;
}

/* Modal form styling */
.modal-dropdown {
  width: 100%;
}

.info-item .cv-text-input,
.info-item .cv-dropdown {
  margin-top: 0.25rem;
}

.action-textarea {
  margin-bottom: 1.5rem;
}

.info-grid {
  row-gap: 1.5rem;
}

/* Tab styling */
.tab-content {
  padding: 1.5rem 0;
}

/* Status tabs styling */
.cv-tabs {
  margin-bottom: 1rem;
}

.cv-tabs .cv-tab {
  color: #f4f4f4;
}

.cv-tabs .cv-tab.cv-tab--selected {
  border-bottom-color: #0f62fe;
}

/* Tab content styling */
.tab-content {
  min-height: 400px;
}

/* Tracking Modal Styles */
.tracking-modal {
  .cv-modal-container {
    max-width: 95vw;
    width: 1200px;
  }
}

.tracking-modal-content {
  padding: 1rem;
  min-height: 600px;
}

.tracking-tab-content {
  padding: 1.5rem;
  min-height: 500px;
}

.tracking-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #444444;
}

.tracking-section-title {
  color: #f4f4f4;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  border-bottom: 1px solid #444444;
  padding-bottom: 0.5rem;
}

.action-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-label {
  color: #8d8d8d;
  font-size: 0.875rem;
  font-weight: 500;
}

.summary-value {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 400;
}

.summary-value.priority-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
}

.summary-value.priority-badge.high {
  background-color: rgba(250, 77, 86, 0.2);
  color: #ff8389;
}

.summary-value.priority-badge.medium {
  background-color: rgba(255, 196, 0, 0.2);
  color: #ffcc00;
}

.summary-value.priority-badge.low {
  background-color: rgba(66, 190, 101, 0.2);
  color: #6fdc8c;
}

.updates-list {
  max-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.update-item {
  background-color: #262626;
  border-radius: 6px;
  padding: 1rem;
  border-left: 3px solid #0f62fe;
}

.update-date {
  color: #8d8d8d;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.update-content {
  color: #f4f4f4;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 0.5rem;
}

.update-author {
  color: #8d8d8d;
  font-size: 0.75rem;
  font-style: italic;
}

.no-updates {
  color: #8d8d8d;
  font-style: italic;
  text-align: center;
  padding: 2rem;
}

/* Action Items List */
.action-items-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.action-item-card {
  background-color: #262626;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #444444;
  transition: all 0.2s ease;
}

.action-item-card.completed {
  border-left: 4px solid #42be65;
  background-color: rgba(66, 190, 101, 0.05);
}

.action-item-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.completion-date {
  color: #42be65;
  font-size: 0.875rem;
  font-weight: 500;
}

.last-updated {
  color: #8d8d8d;
  font-size: 0.875rem;
  font-style: italic;
}

.action-item-description {
  color: #f4f4f4;
  font-size: 0.875rem;
  line-height: 1.4;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #333333;
  border-radius: 4px;
  border-left: 3px solid #0f62fe;
}

.action-item-notes {
  margin-top: 1rem;
}

.notes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.notes-header h5 {
  color: #f4f4f4;
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
}

.add-note-inline {
  background-color: #333333;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #444444;
}

.note-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.75rem;
}

.no-action-items {
  text-align: center;
  padding: 2rem;
  color: #8d8d8d;
}

.no-action-items p {
  margin-bottom: 1rem;
}

/* Action Notes Section */
.action-notes-section {
  margin-bottom: 1.5rem;
}

.action-notes-section h4 {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
}

.notes-list {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 1rem;
}

.note-item {
  background-color: #262626;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  border-left: 3px solid #42be65;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.note-date {
  color: #8d8d8d;
  font-size: 0.875rem;
  font-weight: 500;
}

.note-author {
  color: #8d8d8d;
  font-size: 0.75rem;
  font-style: italic;
}

.note-content {
  color: #f4f4f4;
  font-size: 0.875rem;
  line-height: 1.4;
}

.no-notes {
  color: #8d8d8d;
  font-style: italic;
  text-align: center;
  padding: 1rem;
}

.add-note-section {
  background-color: #262626;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #444444;
}

/* Updates History Section */
.updates-history-section h4 {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
}

.add-update-section {
  background-color: #262626;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #444444;
}

.add-update-section h4 {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
}

.chart-container {
  background-color: #262626;
  border-radius: 6px;
  padding: 1rem;
  min-height: 350px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-chart-data {
  text-align: center;
  color: #8d8d8d;
}

.no-chart-data p {
  margin: 0.5rem 0;
}

.chart-note {
  font-size: 0.875rem;
  font-style: italic;
}

/* Alert History Styles */
.alert-history-section {
  padding: 1.5rem;
}

.section-title {
  color: #f4f4f4;
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0 0 0.5rem 0;
}

.section-description {
  color: #8d8d8d;
  font-size: 0.875rem;
  margin: 0 0 1.5rem 0;
  line-height: 1.4;
}

.alert-history-table {
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
}

.alert-history-row.alert-row {
  background-color: rgba(250, 77, 86, 0.1);
  border-left: 3px solid #fa4d56;
}

.alert-history-row.normal-row {
  background-color: rgba(66, 190, 101, 0.05);
  border-left: 3px solid #42be65;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.alert {
  background-color: #fa4d56;
  animation: pulse-red 2s infinite;
}

.status-indicator.normal {
  background-color: #42be65;
}

.status-text.alert {
  color: #ff8389;
  font-weight: 500;
}

.status-text.normal {
  color: #6fdc8c;
  font-weight: 500;
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 0 0 0 rgba(250, 77, 86, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(250, 77, 86, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(250, 77, 86, 0);
  }
}

.no-alert-data {
  text-align: center;
  padding: 3rem;
  color: #8d8d8d;
}

.no-alert-data p {
  margin: 0.5rem 0;
}

.no-alert-data .note {
  font-size: 0.875rem;
  font-style: italic;
}

/* New Alert Functionality Styles */
.new-alerts-section {
  padding: 1.5rem;
}

.subsection-title {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
  border-bottom: 1px solid #444444;
  padding-bottom: 0.5rem;
}

.ai-insight-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #444444;
}

.ai-insight-content {
  background-color: #262626;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #525252;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #f4f4f4;
}

.add-alert-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #444444;
}

.alert-updates-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #444444;
}

.alert-updates-table {
  background-color: #262626;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #525252;
}

.updates-header {
  display: grid;
  grid-template-columns: 120px 1fr 150px;
  background-color: #393939;
  border-bottom: 1px solid #525252;
}

.update-column {
  padding: 0.75rem;
  font-weight: 600;
  color: #f4f4f4;
  border-right: 1px solid #525252;
}

.update-column:last-child {
  border-right: none;
}

.update-row {
  display: grid;
  grid-template-columns: 120px 1fr 150px;
  border-bottom: 1px solid #525252;
}

.update-row:last-child {
  border-bottom: none;
}

.update-cell {
  padding: 0.75rem;
  color: #f4f4f4;
  border-right: 1px solid #525252;
  font-size: 0.875rem;
}

.update-cell:last-child {
  border-right: none;
}

.no-updates-message {
  text-align: center;
  color: #8d8d8d;
  font-style: italic;
  padding: 2rem;
}

@media (max-width: 768px) {
  .updates-header,
  .update-row {
    grid-template-columns: 1fr;
  }

  .update-column,
  .update-cell {
    border-right: none;
    border-bottom: 1px solid #525252;
  }
}

/* Performance Chart & History Tab Styles */
.chart-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #444444;
}

.performance-history-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #444444;
}

.section-description {
  color: #c6c6c6;
  font-size: 0.875rem;
  margin-bottom: 2rem;
}


.bx--expandable-row:hover {
  background-color: transparent !important;
}

</style>


<template>
  <div class="dashboard-container">
    <!-- Inherit the MainHeader component -->
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" />

    <main class="main-content">
      <!-- Page Header -->
      <div class="page-header">
        <h1 class="page-title">Action Tracker</h1>
        <div class="action-controls">
          <cv-button kind="primary" @click="openNewActionModal">Add New Action</cv-button>
          <cv-button kind="tertiary" @click="openNewAIModal">Ask AI</cv-button>
          <cv-button kind="secondary" @click="exportData">Export</cv-button>
        </div>
      </div>

      <!-- Filter Bar -->
      <div class="filter-bar">
        
        <div class="filter-group">
          <div class="filter-label">Commodity:</div>
          <cv-dropdown
            v-model="commodityFilter"
            label="Filter by commodity"
            :items="commodityOptions"
          ></cv-dropdown>
        </div>

        <div class="filter-group">
          <div class="filter-label">Condense By:</div>
          <cv-dropdown
            v-model="categoryFilter"
            label="Filter category"
            :items="categoryOptions"
          ></cv-dropdown>
        </div>

        <div class="filter-group">
          <div class="filter-label">Assignee:</div>
          <cv-dropdown
            v-model="assigneeFilter"
            label="Filter by assignee"
            :items="assigneeOptions"
          ></cv-dropdown>
        </div>

        <div class="search-box">
          <cv-search
            v-model="searchQuery"
            label="Search"
            placeholder="Search actions..."
          ></cv-search>
        </div>
      </div>

      <!-- Status Tabs -->
      <cv-tabs @tab-selected="onTabSelected" :selected="selectedTab">
        <cv-tab id="new" label="New Issues">
          <div class="tab-content">
            <cv-accordion v-if="categoryFilter === 'Severity'">
               <cv-accordion-item  v-for="[priority, count] in Object.entries(priorityCounts['In-Progress'])" :key="priority">
    <template v-slot:title> {{ priority }} Severity - {{ count }} Items</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.priority === priority && r.status === 'In-Progress')" :key="row.id">
                          
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                          
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>


            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Group'">
               <cv-accordion-item  v-for="[group, count] in Object.entries(groupCounts)" :key="group">
    <template v-slot:title> {{ group }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.group === group && r.status === 'In-Progress')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button class="view-button" @click="handleTrackItem(row)">View</cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Next Update'">
               <cv-accordion-item  v-for="[update, count] in Object.entries(updateCounts)" :key="update">
    <template v-slot:title> {{ update }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.deadline=== update && r.status === 'In-Progress')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
           <cv-data-table v-else :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.status === 'In-Progress') " :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </div>
        </cv-tab>
        
        <cv-tab id="in-progress" label="Open Items">
          <div class="tab-content">
            <cv-accordion v-if="categoryFilter === 'Severity'">
               <cv-accordion-item  v-for="[priority, count] in Object.entries(priorityCounts['In-Progress'])" :key="priority">
    <template v-slot:title> {{ priority }} Severity - {{ count }} Items</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.priority === priority && r.status === 'In-Progress')" :key="row.id">
                          
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                          
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>


            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Group'">
               <cv-accordion-item  v-for="[group, count] in Object.entries(groupCounts)" :key="group">
    <template v-slot:title> {{ group }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.group === group && r.status === 'In-Progress')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button class="view-button" @click="handleView(row)">View</cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Next Update'">
               <cv-accordion-item  v-for="[update, count] in Object.entries(updateCounts)" :key="update">
    <template v-slot:title> {{ update }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.deadline=== update && r.status === 'In-Progress')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
           <cv-data-table v-else :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.status === 'In-Progress') " :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </div>
        </cv-tab>

        <cv-tab id="rejected" label="Rejected Items">
          <div class="tab-content">
            <cv-accordion v-if="categoryFilter === 'Severity'">
               <cv-accordion-item  v-for="[priority, count] in Object.entries(priorityCounts['In-Progress'])" :key="priority">
    <template v-slot:title> {{ priority }} Severity - {{ count }} Items</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.priority === priority && r.status === 'In-Progress')" :key="row.id">
                          
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                          
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>


            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Group'">
               <cv-accordion-item  v-for="[group, count] in Object.entries(groupCounts)" :key="group">
    <template v-slot:title> {{ group }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.group === group && r.status === 'In-Progress')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button class="view-button" @click="handleView(row)">View</cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Next Update'">
               <cv-accordion-item  v-for="[update, count] in Object.entries(updateCounts)" :key="update">
    <template v-slot:title> {{ update }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.deadline=== update && r.status === 'In-Progress')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
           <cv-data-table v-else :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.status === 'In-Progress') " :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </div>
        </cv-tab>

        <cv-tab id="monitored" label="Watch List">
          <div class="tab-content">
            <cv-accordion v-if="categoryFilter === 'Severity'">
               <cv-accordion-item  v-for="[priority, count] in Object.entries(priorityCounts['Monitored'])" :key="priority">
    <template v-slot:title> {{ priority }} Severity - {{ count }} Items</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.priority === priority && r.status === 'Monitored')" :key="row.id">
                          
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                          
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>


            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Group'">
               <cv-accordion-item  v-for="[group, count] in Object.entries(groupCounts)" :key="group">
    <template v-slot:title> {{ group }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.group === group && r.status === 'Monitored')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button class="view-button" @click="handleView(row)">View</cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Next Update'">
               <cv-accordion-item  v-for="[update, count] in Object.entries(updateCounts)" :key="update">
    <template v-slot:title> {{ update }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.deadline=== update && r.status === 'Monitored')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
           <cv-data-table v-else :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.status === 'Monitored')" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
            
          </div>
        </cv-tab>
        

        <cv-tab id="resolved" label="Resolved Items">
          <div class="tab-content">
            <cv-accordion v-if="categoryFilter === 'Severity'">
               <cv-accordion-item  v-for="[priority, count] in Object.entries(priorityCounts['Resolved'])" :key="priority">
    <template v-slot:title> {{ priority }} Severity - {{ count }} Items</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.priority === priority)" :key="row.id">
                          
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                          
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>


            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Group'">
               <cv-accordion-item  v-for="[group, count] in Object.entries(groupCounts)" :key="group">
    <template v-slot:title> {{ group }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.group === group)" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button class="view-button" @click="handleView(row)">View</cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
            <cv-accordion v-else-if="categoryFilter === 'Next Update'">
               <cv-accordion-item  v-for="[update, count] in Object.entries(updateCounts)" :key="update">
    <template v-slot:title> {{ update }} - {{ count }} Item(s)</template>
          <template v-slot:content>
           <cv-data-table  :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows.filter(r => r.deadline=== update)" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </template>
        </cv-accordion-item>
            </cv-accordion>
           <cv-data-table v-else :columns="lvl3Columns"  :expandable="true">
                        <template #data>
                          <cv-data-table-row v-for="row in rows" :key="row.id">
                          <cv-data-table-cell>{{ row.notes }}</cv-data-table-cell>
                          <cv-data-table-cell>{{ row.deadline }}</cv-data-table-cell>
                          <cv-data-table-cell>
                          <cv-button
                            size="small"
                            kind="tertiary"
                            @click="$emit('track-item', item)"
                          >
                            View
                          </cv-button>
                          </cv-data-table-cell>
                        </cv-data-table-row>
                        </template>
                        
                        </cv-data-table>
          </div>
        </cv-tab>
      </cv-tabs>
    </main>

    <!-- Action Details Modal -->
    <cv-modal

      :visible="modalVisible"
      @modal-hidden="modalVisible = false"

    >
      <template slot="title">
        <div>Action Details - {{ selectedRow ? selectedRow.pn : '' }}</div>
      </template>
      <template slot="content">
        <div class="modal-content" v-if="selectedRow">
          <!-- Status Banner -->
          <div class="status-banner" :class="'status-banner-' + selectedRow.status.toLowerCase()">
            <div class="status-badge" :class="selectedRow.status.toLowerCase()">
              <span class="status-indicator" :class="'status-' + selectedRow.status.toLowerCase()"></span>
              {{ selectedRow.status }}
            </div>
            <div class="assignee-info">Assigned to: <strong>{{ selectedRow.assignee }}</strong></div>
          </div>

          <!-- Basic Information Section -->
          <div class="modal-section">
            <div class="section-title">Basic Information</div>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">Commodity</span>
                <cv-dropdown
                  v-model="selectedRow.commodity"
                  label="Commodity"
                  :items="commodityOptions.filter(item => item !== 'All')"
                  class="modal-dropdown"
                ></cv-dropdown>
              </div>
              <div class="info-item">
                <span class="info-label">Group</span>
                <cv-text-input
                  v-model="selectedRow.group"
                  label="Group"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Part Number</span>
                <cv-text-input
                  v-model="selectedRow.pn"
                  label="Part Number"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Test</span>
                <cv-text-input
                  v-model="selectedRow.editableTest"
                  label="Test"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Deadline</span>
                <cv-text-input
                  type="date"
                  v-model="selectedRow.deadline"
                  label="Deadline"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Expected Resolution</span>
                <cv-text-input
                  type="date"
                  v-model="selectedRow.expectedResolution"
                  label="Expected Resolution"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Status</span>
                <cv-dropdown
                  v-model="selectedRow.status"
                  label="Status"
                  :items="statusOptions.filter(item => item !== 'All')"
                  class="modal-dropdown"
                ></cv-dropdown>
              </div>
              <div class="info-item">
                <span class="info-label">Assignee</span>
                <cv-text-input
                  v-model="selectedRow.assignee"
                  label="Assignee"
                ></cv-text-input>
              </div>
              <div class="info-item">
                <span class="info-label">Created</span>
                <span class="info-value">{{ formatDate(selectedRow.createdAt) }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Last Updated</span>
                <span class="info-value">{{ formatDate(selectedRow.updatedAt) }}</span>
              </div>
            </div>
          </div>

          <!-- Action Details Section -->
          <div class="modal-section">
            <div class="section-title">Action Details</div>
            <div class="action-details">
              <div class="action-title">Current Action</div>
              <cv-text-area
                v-model="selectedRow.action"
                label="Action Description"
                class="action-textarea"
              ></cv-text-area>

              <div class="expected-improvements">
                <div class="action-title">Expected Improvements</div>
                <cv-text-area
                  v-model="selectedRow.expectedImprovements"
                  label="Expected Improvements"
                  placeholder="Describe the expected improvements (e.g., 30% increase in yield, 15% reduction in defects)"
                  class="action-textarea"
                ></cv-text-area>
              </div>

              <div class="progress-section">
                <div class="action-title">Progress</div>
                <cv-progress
                  :value="selectedRow.progress || 0"
                  :label-text="`${selectedRow.progress || 0}%`"
                  class="detail-progress-bar"
                />
                <div class="progress-update-controls">
                  <!-- <cv-slider
                    v-model="selectedRow.progress"
                    :min="0"
                    :max="100"
                    :step="5"
                    :label="'Update Progress'"
                  ></cv-slider> -->
                  <cv-button
                    kind="primary"
                    size="small"
                    @click="updateProgress(selectedRow)"
                  >Update Progress</cv-button>
                </div>
              </div>

              <div v-if="selectedRow.updates && selectedRow.updates.length > 0">
                <div class="action-title">Updates History</div>
                <div class="updates-list">
                  <div v-for="(update, index) in selectedRow.updates" :key="index" class="update-item">
                    <div class="update-header">
                      <span class="update-date">{{ formatDate(update.date) }}</span>
                      <span class="update-by">by {{ update.updatedBy }}</span>
                    </div>
                    <div class="update-content">{{ update.content }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Add Update Section -->
          <div class="modal-section">
            <div class="section-title">Add Update</div>
            <div class="add-update-form">
              <cv-text-area
                v-model="newUpdate"
                label="Update Content"
                placeholder="Enter update details..."
              ></cv-text-area>
              <div class="update-form-actions">
                <cv-button
                  kind="primary"
                  @click="addUpdate(selectedRow)"
                >Add Update</cv-button>
              </div>
            </div>
          </div>

          <!-- Issues Section -->
          <div class="modal-section" v-if="selectedRow.issues && selectedRow.issues.length > 0">
            <div class="section-title">Related Issues</div>
            <cv-data-table
              :columns="issueColumns"
              :data="selectedRow.issues"
              :title="''"
            ></cv-data-table>
          </div>

          <!-- Notes Section -->
          <div class="modal-section">
            <div class="section-title">Notes</div>
            <cv-text-area
              v-model="selectedRow.notes"
              label="Additional Notes"
              placeholder="Enter any additional notes"
            ></cv-text-area>
          </div>

          <!-- Action Buttons -->
          <div class="modal-actions">
            <cv-button kind="secondary" @click="modalVisible = false">Cancel</cv-button>
            <cv-button kind="primary" @click="updateEntireAction(selectedRow)">Update Action</cv-button>
          </div>
        </div>
      </template>
    </cv-modal>

    <!-- New Action Modal -->
    <cv-modal
      class="action-modal"
      :visible="newActionModalVisible"
      @modal-hidden="newActionModalVisible = false"
      :size="'lg'"
    >
      <template slot="title">
        <div>Create New Action</div>
      </template>
      <template slot="content">
        <div class="modal-content">
          <!-- Basic Information Section -->
          <div class="modal-section">
            <div class="section-title">Basic Information</div>
            <p class="form-note">Fields marked with <span class="required-field">*</span> are required</p>
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">Process/Commodity <span class="required-field">*</span></label>
                <cv-dropdown
                  v-model="newAction.commodity"
                  label="Select Process/Commodity"
                  :items="commodityOptions.filter(item => item !== 'All')"
                ></cv-dropdown>
              </div>

              <div class="form-group">
                <label class="form-label">Part Group <span class="required-field">*</span></label>
                <cv-text-input
                  v-model="newAction.group"
                  label="Part Group"
                  placeholder="Enter part group name"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Part Number</label>
                <cv-text-input
                  v-model="newAction.pn"
                  label="Part Number"
                  placeholder="Enter part number"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Test</label>
                <cv-text-input
                  v-model="newAction.test"
                  label="Test"
                  placeholder="Enter test name"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Deadline</label>
                <cv-text-input
                  type="date"
                  v-model="newAction.deadline"
                  label="Deadline"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Expected Resolution Date</label>
                <cv-text-input
                  type="date"
                  v-model="newAction.expectedResolution"
                  label="Expected Resolution"
                ></cv-text-input>
              </div>

              <div class="form-group">
                <label class="form-label">Status</label>
                <cv-dropdown
                  v-model="newAction.status"
                  label="Select Status"
                  :items="['Current', 'In-Progress', 'Monitored', 'Resolved']"
                ></cv-dropdown>
              </div>

              <div class="form-group">
                <label class="form-label">Priority</label>
                <cv-dropdown
                  v-model="newAction.priority"
                  label="Select Priority"
                  :items="['High', 'Medium', 'Low']"
                ></cv-dropdown>
              </div>

              <!-- <div class="form-group">
                <label class="form-label">Progress (%)</label>
                <cv-slider
                  v-model="newAction.progress"
                  :min="0"
                  :max="100"
                  :step="5"
                  :label="'Progress'"
                ></cv-slider>
              </div> -->

              <div class="form-group">
                <label class="form-label">Assignee</label>
                <cv-text-input
                  v-model="newAction.assignee"
                  label="Assignee"
                  placeholder="Enter assignee name"
                ></cv-text-input>
              </div>
            </div>
          </div>

          <!-- Action Details Section -->
          <div class="modal-section">
            <div class="section-title">Action Details</div>
            <div class="form-group full-width">
              <label class="form-label">Action Description <span class="required-field">*</span></label>
              <cv-text-area
                v-model="newAction.action"
                label="Action Description"
                placeholder="Describe the action to be taken"
              ></cv-text-area>
            </div>

            <div class="form-group full-width">
              <label class="form-label">Expected Improvements</label>
              <cv-text-area
                v-model="newAction.expectedImprovements"
                label="Expected Improvements"
                placeholder="Describe the expected improvements (e.g., 30% increase in yield, 15% reduction in defects)"
              ></cv-text-area>
            </div>
          </div>

          <!-- Notes Section -->
          <div class="modal-section">
            <div class="section-title">Notes</div>
            <div class="form-group full-width">
              <label class="form-label">Additional Notes</label>
              <cv-text-area
                v-model="newAction.notes"
                label="Notes"
                placeholder="Enter any additional notes"
              ></cv-text-area>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="modal-actions">
            <cv-button kind="secondary" @click="newActionModalVisible = false">Cancel</cv-button>
            <cv-button kind="primary" @click="createNewAction">Create Action</cv-button>
          </div>
        </div>
      </template>
    </cv-modal>

    <!-- New AI Modal -->
    <cv-modal
      class="action-modal"
      :visible="newAIModalVisible"
      @modal-hidden="newAIModalVisible = false"
      :size="'lg'"
    >
      <template slot="title">
        <div>Ask watsonx a Question</div>
      </template>
      <template slot="content">
        <div class="modal-content">

         <div class="form-group">
                
                <cv-text-input
                  v-model="AIprompt.question"
                  label="What would you like to know?"
                  placeholder="Ask a Question..."
                ></cv-text-input>
              </div>

          <!-- Action Buttons -->
          <div class="modal-actions">
            <cv-button kind="secondary" @click="newAIModalVisible = false">Cancel</cv-button>
            <cv-button kind="primary" @click="sendAIPrompt">Ask watsonx</cv-button>
          </div>
        </div>
      </template>
    </cv-modal>

    <!-- Tracking Modal -->
    <cv-modal
      class="tracking-modal"
      :visible="trackingModalVisible"
      @modal-hidden="trackingModalVisible = false"
      :size="'xl'"
    >
      <template slot="title">
        <div>Action Tracking - {{ selectedTrackingItem ? selectedTrackingItem.group : '' }}</div>
      </template>
      <template slot="content">
        <div class="tracking-modal-content" v-if="selectedTrackingItem">
          <!-- Tracking Tabs -->
          <cv-tabs>
            <cv-tab id="action-items-tab" label="Action Items">
              <div class="tracking-tab-content">
                <div class="tracking-section">
                  <h3 class="tracking-section-title">Action Details</h3>
                  <div class="action-summary">
                    <div class="summary-item">
                      <span class="summary-label">Part Group:</span>
                      <span class="summary-value">{{ selectedTrackingItem.group }}</span>
                    </div>
                    <div class="summary-item">
                      <span class="summary-label">Part Number:</span>
                      <span class="summary-value">{{ selectedTrackingItem.pn }}</span>
                    </div>
                    <div class="summary-item">
                      <span class="summary-label">Status:</span>
                      <span class="summary-value">{{ selectedTrackingItem.status }}</span>
                    </div>
                    <div class="summary-item">
                      <span class="summary-label">Priority:</span>
                      <span class="summary-value priority-badge" :class="selectedTrackingItem.priority.toLowerCase()">
                        {{ selectedTrackingItem.priority }}
                      </span>
                    </div>
                    <div class="summary-item">
                      <span class="summary-label">Progress:</span>
                      <span class="summary-value">{{ selectedTrackingItem.progress }}%</span>
                    </div>
                  </div>
                </div>

              <div class="tracking-section">
                <h3 class="tracking-section-title">Action Items & Progress</h3>

                <!-- Action Items List -->
                <div class="action-items-list">
                  <div
                    v-for="(actionItem, index) in selectedTrackingItem.actionItems"
                    :key="index"
                    class="action-item-card"
                    :class="{ 'completed': actionItem.completed }"
                  >
                    <!-- Action Item Header -->
                    <div class="action-item-header">
                      <cv-checkbox
                        v-model="actionItem.completed"
                        :label="actionItem.title"
                        @change="updateActionItemCompletion(actionItem, index)"
                      />
                      <span class="completion-date" v-if="actionItem.completed && actionItem.completedDate">
                        Completed on {{ formatDate(actionItem.completedDate) }}
                      </span>
                      <span class="last-updated" v-if="actionItem.lastUpdated">
                        Last updated: {{ formatDate(actionItem.lastUpdated) }}
                      </span>
                    </div>

                    <!-- Action Item Description -->
                    <div class="action-item-description" v-if="actionItem.description">
                      {{ actionItem.description }}
                    </div>

                    <!-- Action Item Notes -->
                    <div class="action-item-notes">
                      <div class="notes-header">
                        <h5>Notes</h5>
                        <cv-button
                          size="small"
                          kind="ghost"
                          @click="toggleAddNote(index)"
                        >
                          Add Note
                        </cv-button>
                      </div>

                      <!-- Add Note Section -->
                      <div v-if="actionItem.showAddNote" class="add-note-inline">
                        <cv-text-area
                          v-model="actionItem.newNote"
                          placeholder="Enter note for this action item..."
                          rows="2"
                        ></cv-text-area>
                        <div class="note-actions">
                          <cv-button
                            kind="primary"
                            size="small"
                            @click="addNoteToActionItem(actionItem, index)"
                            :disabled="!actionItem.newNote || !actionItem.newNote.trim()"
                          >
                            Save Note
                          </cv-button>
                          <cv-button
                            kind="secondary"
                            size="small"
                            @click="cancelAddNote(index)"
                          >
                            Cancel
                          </cv-button>
                        </div>
                      </div>

                      <!-- Notes List -->
                      <div class="notes-list">
                        <div
                          v-for="(note, noteIndex) in actionItem.notes"
                          :key="noteIndex"
                          class="note-item"
                        >
                          <div class="note-header">
                            <span class="note-date">{{ formatDate(note.date) }}</span>
                            <span class="note-author">by {{ note.author }}</span>
                          </div>
                          <div class="note-content">{{ note.content }}</div>
                        </div>
                        <div v-if="!actionItem.notes || actionItem.notes.length === 0" class="no-notes">
                          No notes for this item yet
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- No Action Items -->
                  <div v-if="!selectedTrackingItem.actionItems || selectedTrackingItem.actionItems.length === 0" class="no-action-items">
                    <p>No action items defined for this tracking item.</p>
                    <cv-button
                      kind="primary"
                      size="small"
                      @click="addDefaultActionItems"
                    >
                      Create Default Action Items
                    </cv-button>
                  </div>
                </div>
              </div>
            </div>
            </cv-tab>

            <cv-tab id="new-alerts-tab" label="Alert History">
              <div class="tracking-tab-content">
                <div class="new-alerts-section">
                  <h3 class="section-title">Alert Management</h3>
                  <p class="section-description">Manage alerts and updates for {{ selectedTrackingItem.group }}</p>

                  <!-- AI Insight Section -->
                  <!-- <div class="ai-insight-section">
                    <h4 class="subsection-title">AI Insight</h4>
                    <div v-if="isLoadingAiInsight" class="loading-message">
                      Generating AI insight...
                    </div>
                    <div v-else class="ai-insight-content">
                      {{ aiInsight || 'No AI insight available for this alert.' }}
                    </div>
                  </div> -->

                  <!-- Add New Alert Update -->
                  <!-- <div class="add-alert-section">
                    <h4 class="subsection-title">Add Alert Update</h4>
                    <div class="add-update-form">
                      <cv-text-area
                        v-model="newAlertUpdate"
                        label="Update Content"
                        placeholder="Enter update details..."
                        rows="4"
                      ></cv-text-area>
                      <div class="update-form-actions">
                        <cv-button
                          kind="primary"
                          @click="addAlertUpdate"
                          :disabled="!newAlertUpdate.trim()"
                        >
                          Add Update
                        </cv-button>
                      </div>
                    </div>
                  </div> -->

                  <!-- Alert History Table -->
                  <div class="alert-updates-section">
                    <h4 class="subsection-title">Alert History</h4>
                        <cv-data-table
                    :columns="alertHistoryColumns"
                    :title="''"
                    :expandable = "true"
                  >
                    <template #data>
                      <cv-data-table-row
                        v-for="(record, index) in alertHistoryData"
                        :key="index"
                        :class="getAlertRowClass(record)"
                      >
                        <cv-data-table-cell>{{ record.date }}</cv-data-table-cell>
                        <cv-data-table-cell>
                          <div class="status-cell">
                            <span class="status-indicator" :class="record.status.toLowerCase()"></span>
                            <span class="status-text" :class="record.status.toLowerCase()">{{ record.status }}</span>
                          </div>
                        </cv-data-table-cell>
                        <cv-data-table-cell>{{ record.description || 'N/A' }}</cv-data-table-cell>
                        <cv-data-table-cell>
                          <div class="status-cell">
                            <span class="status-indicator" :class="record.status.toLowerCase()"></span>
                            <span class="status-text" :class="record.status.toLowerCase()">{{ record.status }}</span>
                          </div>
                        </cv-data-table-cell>
                        <template #expandedContent>{{"test content"}}
                        </template>
                      </cv-data-table-row>
                    </template>
                  </cv-data-table>

                      <!-- <div v-if="alertUpdates.length > 0" class="alert-updates-table">
                        <div class="updates-header">
                          <div class="update-column">Date</div>
                          <div class="update-column">Update</div>
                          <div class="update-column">Updated By</div>
                        </div>
                        <div
                          v-for="(update, index) in alertUpdates"
                          :key="index"
                          class="update-row"
                        >
                          <div class="update-cell">{{ update.date }}</div>
                          <div class="update-cell">{{ update.update }}</div>
                          <div class="update-cell">{{ update.updatedBy }}</div>
                        </div>
                      </div>
                      <div v-else class="no-updates-message">
                        No alert updates available.
                      </div> -->
                  </div>
                </div>
              </div>
            </cv-tab>

            <cv-tab id="performance-tab" label="Performance Chart & History">
              <div class="tracking-tab-content">
                <!-- Performance Chart Section -->
                <div class="chart-section" v-if="perfHistoryData.length > 0">
                  <h3 class="section-title">Performance Chart</h3>
                  <LineChart :data="perfHistoryData"
                  :options="chartOptions"
                  :loading="isPerfHistoryLoading"/>
                </div>

                <!-- Performance History Table Section -->
                <div class="performance-history-section">
                  <h3 class="section-title">Monthly Performance History</h3>
                  <p class="section-description">Historical record of performance and status for {{ selectedTrackingItem.group }}</p>

                  <!-- <cv-data-table
                    :columns="alertHistoryColumns"
                    :title="''"
                    class="alert-history-table"
                  >
                    <template slot="data">
                      <cv-data-table-row
                        v-for="(record, index) in perfHistoryData"
                        :key="index"
                        :class="getAlertRowClass(record)"
                      >
                        <cv-data-table-cell>{{ record.month }}</cv-data-table-cell>
                        <cv-data-table-cell>{{ record.year }}</cv-data-table-cell>
                        <cv-data-table-cell>
                          <div class="status-cell">
                            <span class="status-indicator" :class="record.status.toLowerCase()"></span>
                            <span class="status-text" :class="record.status.toLowerCase()">{{ record.status }}</span>
                          </div>
                        </cv-data-table-cell>
                        <cv-data-table-cell>{{ record.actualRate }}%</cv-data-table-cell>
                        <cv-data-table-cell>{{ record.targetRate }}%</cv-data-table-cell>
                        <cv-data-table-cell>{{ record.xFactor }}</cv-data-table-cell>
                        <cv-data-table-cell>{{ record.volume }}</cv-data-table-cell>
                        <cv-data-table-cell>{{ record.defects }}</cv-data-table-cell>
                        <cv-data-table-cell>{{ record.notes || 'N/A' }}</cv-data-table-cell>
                      </cv-data-table-row>
                    </template>
                  </cv-data-table> -->

                  <div v-if="!perfHistoryData || perfHistoryData.length === 0" class="no-alert-data">
                    <p>No performance history available for this part</p>
                    <p class="note">Performance history will be populated as data becomes available</p>
                  </div>
                </div>
              </div>
            </cv-tab>
          </cv-tabs>
        </div>
      </template>
    </cv-modal>
  </div>
</template>



<script>
import MainHeader from '@/components/MainHeader'; // Import the MainHeader component
import LineChart from '@/components/LineChart/LineChart.vue'; // Import the LineChart component
// import PerformanceChart from '@/components/PerformanceChart/PerformanceChart.vue'; // Import the PerformanceChart component

export default {
  name: 'ActionTracker',
  components: {
    MainHeader,

    LineChart
  },
  data() {
    return {
      // Dynamic columns based on status type
      priorityCounts: {'In-Progress': {High:0, Medium:0, Low:0}, Monitored: {High:0, Medium:0, Low:0}, Resolved: {High:0, Medium:0, Low:0} },
      groupCounts: {},
      updateCounts: {},
      lvl1Columns: ['Priority', '#Items'],
      lvl2ColumnsGroup: ['Group', '# Items'],
      lvl2ColumnsSev: ['Severity', '# Items'],
      lvl3Columns: ['Description', 'Update Date', 'Details'],
      inProgressColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Deadline', 'Date Started', 'Assignee', 'Tracking'],
      monitoredColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Monitor Date', 'Date Started', 'Assignee', 'Tracking'],
      resolvedColumns: ['Priority', 'Commodity', 'Group', 'PN', 'Test', 'Resolution Date', 'Date Started', 'Assignee', 'Tracking'],
      issueColumns: ['Issue ID', 'Description', 'Severity', 'Date Reported'],
      historyColumns: ['Date', 'Action', 'Updated By'],
      alertHistoryColumns: ['Date', 'Description', 'Status'],
      perfHistoryColumns: ['Month', 'Year', 'Status', 'Actual Rate', 'Target Rate', 'X-Factor', 'Volume', 'Defects', 'Notes'],
      modalVisible: false,
      newActionModalVisible: false,
      newAIModalVisible: false,
      trackingModalVisible: false,
      selectedRow: null,
      selectedTrackingItem: null,
      searchQuery: '',
      categoryFilter: 'None',
      commodityFilter: 'All',
      assigneeFilter: 'All',
      commodityOptions: ['All', 'FUL', 'FAB', 'Power', 'Cable', 'Memory'],
      categoryOptions: ['None', 'Group', 'Severity', 'Next Update'],
      assigneeOptions: ['All'],
      selectedTab: 'in-progress',
      expandedSideNav: false,
      useFixed: true,
      isLoading: false,
      loadingError: null,
      AIprompt: "",
      isPerfHistoryLoading: true,
      newAction: {
        commodity: '',
        group: '',
        pn: '',
        test: '',
        deadline: '',
        expectedResolution: '',
        expectedImprovements: '',
        progress: 0,
        status: 'Current',
        priority: 'Medium',
        assignee: '',
        action: '',
        notes: '',
        source: 'manual' // 'manual' or 'pqe'
      },
      newUpdate: '',
      newTrackingUpdate: '',
      newActionNote: '',
      performanceChartData: [],
      perfHistoryData: [],
      alertHistoryData: [],

      // New Alert functionality
      newAlertUpdate: '',
      alertUpdates: [],
      aiInsight: '',
      // isLoadingAiInsight: false,
      chartOptions: {
        title: 'XFactor Trend by Breakout Group',
        axes: {
          bottom: {
            title: 'Period',
            mapsTo: 'date',
            scaleType: 'time',
            domain: [new Date('2024-01-01'), new Date('2025-12-31')], // Set explicit domain to show all dates
            ticks: {
              number: 12 // Show more ticks for better readability
            },
            formatters: {
              tick: (date) => {
                // Format the date to show month and year
                const d = new Date(date);
                return d.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
              }
            }
          },
          left: {
            title: 'XFactor',
            mapsTo: 'value',
            scaleType: 'linear',
            domain: [0, 3.2],
            thresholds: [
              {
                value: 1,
                label: 'Sustained Problem Threshold',
                fillColor: 'red',
                
              },
              {
                value: 3.0,
                label: 'Short-Term Spike Threshold',
                fillColor: 'yellow',
                
              }
            ]
          }
        },
        curve: 'curveMonotoneX',
        height: '400px',
        legend: {
          alignment: 'center',
          enabled: true,
          truncation: {
            type: 'end',
            threshold: 20
          }
        },
        // tooltip: {
        //   enabled: true,
        //   customHTML: (dataPoints) => {
        //     const dataPoint = dataPoints[0];
        //     if (!dataPoint) return '';

        //     // Ensure we're working with a proper date object
        //     const date = dataPoint.date instanceof Date ? dataPoint.date : new Date(dataPoint.date);
        //     const formattedDate = date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' });

        //     // Get target rate if available
        //     const targetRate = dataPoint.data && dataPoint.data.targetRate ?
        //       dataPoint.data.targetRate :
        //       (this.xFactorData[dataPoint.group] && this.xFactorData[dataPoint.group].targetRate ?
        //         this.xFactorData[dataPoint.group].targetRate : 'N/A');

        //     return `
        //       <div class="custom-tooltip">
        //         <p><strong>${dataPoint.group}</strong></p>
        //         <p>Period: ${formattedDate}</p>
        //         <p>XFactor: ${dataPoint.value.toFixed(2)}</p>
        //         <p>Target Rate: ${typeof targetRate === 'number' ? targetRate.toFixed(6) : targetRate}</p>
        //         ${dataPoint.data.defects ? `<p>Defects: ${dataPoint.data.defects}</p>` : ''}
        //         ${dataPoint.data.volume ? `<p>Volume: ${dataPoint.data.volume}</p>` : ''}
        //       </div>
        //     `;
        //   }
        // },

        data: {
          loading: this.isLoading
        },
        zoomBar: {
          top: {
            enabled: false
          }
        }
      },
      rows: [],
    };
  },
  computed: {

        // Filter rows based on search and filters
    filteredRows() {
      return this.rows.filter(row => {
        // Filter by commodity
        if (this.commodityFilter !== 'All' && row.commodity !== this.commodityFilter) {
          return false;
        }

        // Filter by assignee
        if (this.assigneeFilter !== 'All' && row.assignee !== this.assigneeFilter) {
          return false;
        }

        // Filter by search query
        if (this.searchQuery) {
          const query = this.searchQuery.toLowerCase();
          return (
            row.commodity.toLowerCase().includes(query) ||
            row.group.toLowerCase().includes(query) ||
            row.pn.toLowerCase().includes(query) ||
            row.action.toLowerCase().includes(query) ||
            row.assignee.toLowerCase().includes(query)
          );
        }

        return true;
      });
    },

    // In-progress items - actively being worked on (includes current items)
    inProgressItems() {
      return this.filteredRows.filter(row =>
        row.status === 'In-Progress' || row.status === 'Active' || row.status === 'Working' ||
        row.status === 'Current' || row.status === 'New' || row.status === 'Open'
      );
    },

    // Monitored items - items being tracked but not actively worked
    monitoredItems() {
      return this.filteredRows.filter(row =>
        row.status === 'Monitored' || row.status === 'Watching' || row.status === 'Blocked'
      );
    },

    // Resolved items - completed or closed items
    resolvedItems() {
      return this.filteredRows.filter(row =>
        row.status === 'Resolved' || row.status === 'Completed' || row.status === 'Closed'
      );
    }
  },
  mounted() {
    // Load action tracker data from API
    this.loadActionTrackerData();

    // Check if we have query parameters to create a new action
    const query = this.$route.query;
    if (query.createAction === 'true') {
      // Populate the new action form with data from query parameters
      this.newAction = {
        commodity: query.commodity || this.commodityOptions.filter(item => item !== 'All')[0] || '',
        group: query.group || '',
        pn: query.pn || '',
        test: query.test || '',
        deadline: query.deadline || new Date().toISOString().split('T')[0],
        status: query.status || 'Current',
        priority: query.priority || 'Medium',
        assignee: query.assignee || '',
        action: query.action || '',
        notes: query.notes || '',
        source: 'pqe' // Mark as coming from PQE dashboard
      };

      // Open the new action modal
      this.newActionModalVisible = true;
    }

    // Check if we have query parameters for alert navigation
    if (query.actionTrackerId && query.tab === 'alerts') {
      // Find the action tracker item and open the tracking modal with alerts tab
      this.$nextTick(() => {
        this.openAlertForActionTracker(query.actionTrackerId, query.alertId);
      });
    }
  },
  methods: {
    // Get authentication config for API calls
    getAuthConfig() {
      const token = localStorage.getItem('token');
      return {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
    },

    // Handle tab selection
    onTabSelected(tabId) {
      this.selectedTab = tabId;
      console.log('Tab selected:', tabId);
    },


    // Handle item selection from table
    handleItemSelected(item) {
      this.selectedRow = item;
      this.modalVisible = true;
      console.log('Item selected:', item);
    },

    // Handle item update from table
    handleUpdateItem(item) {
      console.log('Update item:', item);
      this.updateActionItem(item);
    },

    // Handle tracking item from table
    handleTrackItem(item) {
      console.log('Track item:', item);
      this.selectedTrackingItem = item;
      //this.loadPerformanceData(item);
      this.loadPerfHistoryData(item);
      this.loadAlertHistoryData(item);
      this.loadAlertUpdates(item);
      // this.loadAiInsight(item);
      this.trackingModalVisible = true;
    },

    // Export data functionality
    exportData() {
      console.log('Exporting action tracker data...');
      // TODO: Implement export functionality
      alert('Export functionality will be implemented soon.');
    },

    // Update assignee options based on loaded data
    updateAssigneeOptions() {
      const assignees = [...new Set(this.rows.map(row => row.assignee).filter(assignee => assignee))];
      this.assigneeOptions = ['All', ...assignees.sort()];
    },


    // Process data for the line chart from the consolidated endpoint
    processBreakoutLineChartData(item, breakoutData) {
      if (!breakoutData || !breakoutData.xFactors || Object.keys(breakoutData.xFactors).length === 0) {
        this.breakoutTabNoDataMessage = `No data found for ${this.breakoutTabSelectedGroup} in the selected date range`;
        return;
      }

      // Process chart data
      const dataPoints = [];

      // Track min and max dates for chart domain
      let minDate = new Date('2025-12-31');
      let maxDate = new Date('2024-01-01');
      console.log("HEY2", breakoutData.xFactors)
      Object.entries(breakoutData.xFactors).forEach(([period, data]) => {
        // Make sure period is in YYYY-MM format and convert to a proper date
        const dateParts = period.split('-');
        if (dateParts.length >= 2) {
          const year = parseInt(dateParts[0]);
          const month = parseInt(dateParts[1]) - 1; // JavaScript months are 0-indexed
          const date = new Date(year, month, 1);

          // Update min and max dates
          if (date < minDate) minDate = new Date(date);
          if (date > maxDate) maxDate = new Date(date);

          dataPoints.push({
            date: date,
            value: data.xFactor,
            group: item.group,
            defects: data.defects,
            volume: data.volume,
            targetRate: data.targetRate || (breakoutData.targetRate || 0)
          });
          
        }
      });

      // Sort by date
      dataPoints.sort((a, b) => a.date - b.date);
      // this.breakoutTabChartData = dataPoints;

      // Update chart domain based on actual data
      if (dataPoints.length > 0) {
        // Add padding to the date range (1 month before and after)
        minDate.setMonth(minDate.getMonth() - 1);
        maxDate.setMonth(maxDate.getMonth() + 1);

        // Update chart options with dynamic domain
        this.chartOptions.axes.bottom.domain = [minDate, maxDate];

        this.perfHistoryData = [...this.perfHistoryData, ...dataPoints]; 
        console.log(`Date range in breakout data: ${minDate.toISOString()} to ${maxDate.toISOString()}`);
      }
      console.log("HEY", this.perfHistoryData)
      
      console.log(`Successfully processed line chart data for ${item.group} with ${dataPoints.length} data points`);
    },


    // Load performance history data for tracking
   async loadPerfHistoryData(item) {
  try {
    console.log('Loading alert history data for:', item.group);

    if (!item.group) {
      console.error('No group specified for performance history');
      return;
    }
    this.perfHistoryData = [];

    // Prepare the request body
    const requestBody = {
      breakoutName: item.group,
      startDate: "2024-01",
      endDate: "2025-06",
      baseStartDate: "2024-01",
      exactDateRange: true,
      useMonthFormat: true
    };

    // Get auth headers (same method you use elsewhere)
    const config = this.getAuthConfig(); // should return { headers: { Authorization: 'Bearer ...' } }

    const response = await fetch('/api-statit2/get_metis_breakout_analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...config.headers
      },
      body: JSON.stringify(requestBody)
    });

    const data = await response.json();

    if (data.status_res === 'success') {
      console.log('Received performance history data:', data);

      if (data.xFactorData && data.xFactorData.xFactors) {
        const breakoutData = {xFactors: data.xFactorData.xFactors};
        this.processBreakoutLineChartData(item,breakoutData);
        
        
      } else {
        console.warn('No xFactor data found for performance history');
        this.perfHistoryData = [];
      }

    } else {
      console.error('Error in API response:', data);
      this.perfHistoryData = [];
    }

  } catch (error) {
    console.error('Error loading alert history data:', error);
    this.perfHistoryData = [];
  } finally {
    this.isPerfHistoryLoading = false;
  }
}
,

        // Load performance history data for tracking
    async loadAlertHistoryData(item) {
      try {
        console.log('Loading alert history data for:', item.group);

        // Generate sample alert history data for demonstration
        // In a real implementation, this would fetch actual alert history from the API
        const historyData = this.generateAlertHistoryData();
        this.alertHistoryData = historyData;

      } catch (error) {
        console.error('Error loading alert history data:', error);
        this.alertHistoryData = [];
      }
    },



    // Generate sample alert history data
    generateAlertHistoryData() {
      const data = [];
      const currentDate = new Date();
      const targetRate = 2.5; // Default target rate percentage

      // Generate 12 months of alert data
      for (let i = 11; i >= 0; i--) {
        const date = new Date(currentDate);
        date.setMonth(date.getMonth() - i);

        const month = date.toLocaleString('default', { month: 'long' });
        const year = date.getFullYear();

        // Generate realistic data with some variation
        const volume = Math.floor(Math.random() * 5000) + 1000; // 1000-6000 volume
        const baseDefectRate = targetRate + (Math.random() - 0.5) * 4; // Vary around target
        const actualRate = Math.max(0.1, Math.min(8.0, baseDefectRate)); // 0.1% to 8.0%
        const defects = Math.floor((actualRate / 100) * volume);
        const xFactor = (actualRate / targetRate).toFixed(2);

        // Determine status based on whether actual rate exceeds target
        const status = actualRate > targetRate ? 'Alert' : 'Normal';

        // Generate notes for alert conditions
        let notes = null;
        if (status === 'Alert') {
          const alertReasons = [
            'Exceeded target threshold',
            'Process variation detected',
            'Quality issue identified',
            'Supplier batch issue',
            'Equipment calibration needed',
            'Environmental factors'
          ];
          notes = alertReasons[Math.floor(Math.random() * alertReasons.length)];
        }

        data.push({
          month,
          year,
          status,
          actualRate: actualRate.toFixed(2),
          targetRate: targetRate.toFixed(2),
          xFactor,
          volume: volume.toLocaleString(),
          defects,
          notes
        });
      }

      return data.reverse(); // Most recent first
    },

    // Generate sample alert history data
    generatePerfHistoryData() {
      const data = [];
      const currentDate = new Date();
      const targetRate = 2.5; // Default target rate percentage

      // Generate 12 months of historical data
      for (let i = 11; i >= 0; i--) {
        const date = new Date(currentDate);
        date.setMonth(date.getMonth() - i);

        const month = date.toLocaleString('default', { month: 'long' });
        const year = date.getFullYear();

        // Generate realistic data with some variation
        const volume = Math.floor(Math.random() * 5000) + 1000; // 1000-6000 volume
        const baseDefectRate = targetRate + (Math.random() - 0.5) * 4; // Vary around target
        const actualRate = Math.max(0.1, Math.min(8.0, baseDefectRate)); // 0.1% to 8.0%
        const defects = Math.floor((actualRate / 100) * volume);
        const xFactor = (actualRate / targetRate).toFixed(2);

        // Determine status based on whether actual rate exceeds target
        const status = actualRate > targetRate ? 'Alert' : 'Normal';

        // Generate notes for alert conditions
        let notes = null;
        if (status === 'Alert') {
          const alertReasons = [
            'Exceeded target threshold',
            'Process variation detected',
            'Quality issue identified',
            'Supplier batch issue',
            'Equipment calibration needed',
            'Environmental factors'
          ];
          notes = alertReasons[Math.floor(Math.random() * alertReasons.length)];
        }

        data.push({
          month,
          year,
          status,
          actualRate: actualRate.toFixed(2),
          targetRate: targetRate.toFixed(2),
          xFactor,
          volume: volume.toLocaleString(),
          defects,
          notes
        });
      }

      return data.reverse(); // Most recent first
    },


    // Get CSS class for alert history rows
    getAlertRowClass(record) {
      const classes = ['alert-history-row'];
      if (record.status === 'Alert') {
        classes.push('alert-row');
      } else {
        classes.push('normal-row');
      }
      return classes.join(' ');
    },

    // Update individual action item completion status
    async updateActionItemCompletion(actionItem) {
      try {
        const currentDate = new Date().toISOString().split('T')[0];

        // Update completion date and last updated
        if (actionItem.completed && !actionItem.completedDate) {
          actionItem.completedDate = currentDate;
        } else if (!actionItem.completed) {
          actionItem.completedDate = null;
        }
        actionItem.lastUpdated = currentDate;

        // Sort action items by last updated (most recent first)
        this.selectedTrackingItem.actionItems.sort((a, b) => {
          const dateA = new Date(a.lastUpdated || a.completedDate || '1970-01-01');
          const dateB = new Date(b.lastUpdated || b.completedDate || '1970-01-01');
          return dateB - dateA;
        });

        // Update the action in the API
        const updateData = {
          id: this.selectedTrackingItem.id,
          actionItems: this.selectedTrackingItem.actionItems
        };
        await this.updateActionItem(updateData);

        // Add an update about the completion status change
        const statusMessage = actionItem.completed ?
          `Action item "${actionItem.title}" marked as completed` :
          `Action item "${actionItem.title}" marked as incomplete`;

        await this.addUpdate(this.selectedTrackingItem, statusMessage);

        console.log('Action item completion status updated:', actionItem);

      } catch (error) {
        console.error('Error updating action item completion:', error);
        alert(`Failed to update completion status: ${error.message}`);
        // Revert the checkbox state on error
        actionItem.completed = !actionItem.completed;
      }
    },

    // Toggle add note section for action item
    toggleAddNote(index) {
      const actionItem = this.selectedTrackingItem.actionItems[index];
      actionItem.showAddNote = !actionItem.showAddNote;
      if (actionItem.showAddNote) {
        actionItem.newNote = '';
      }
    },

    // Cancel add note
    cancelAddNote(index) {
      const actionItem = this.selectedTrackingItem.actionItems[index];
      actionItem.showAddNote = false;
      actionItem.newNote = '';
    },

    // Add note to specific action item
    async addNoteToActionItem(actionItem) {
      try {
        if (!actionItem.newNote || !actionItem.newNote.trim()) {
          alert('Please enter a note');
          return;
        }

        const noteData = {
          date: new Date().toISOString().split('T')[0],
          content: actionItem.newNote.trim(),
          author: 'Current User' // In real implementation, get from auth
        };

        // Initialize notes array if it doesn't exist
        if (!actionItem.notes) {
          actionItem.notes = [];
        }

        // Add note to the local data (newest first)
        actionItem.notes.unshift(noteData);

        // Update last updated timestamp
        actionItem.lastUpdated = noteData.date;

        // Sort action items by last updated (most recent first)
        this.selectedTrackingItem.actionItems.sort((a, b) => {
          const dateA = new Date(a.lastUpdated || a.completedDate || '1970-01-01');
          const dateB = new Date(b.lastUpdated || b.completedDate || '1970-01-01');
          return dateB - dateA;
        });

        // Save to API
        const updateData = {
          id: this.selectedTrackingItem.id,
          actionItems: this.selectedTrackingItem.actionItems
        };
        await this.updateActionItem(updateData);

        // Add general update
        await this.addUpdate(this.selectedTrackingItem, `Note added to "${actionItem.title}": ${actionItem.newNote.trim()}`);

        // Clear the input and hide add note section
        actionItem.newNote = '';
        actionItem.showAddNote = false;

        console.log('Note added to action item:', noteData);

      } catch (error) {
        console.error('Error adding note to action item:', error);
        alert(`Failed to add note: ${error.message}`);
      }
    },

    // Add default action items
    addDefaultActionItems() {
      if (!this.selectedTrackingItem.actionItems) {
        this.selectedTrackingItem.actionItems = [];
      }

      const defaultItems = [
        {
          title: 'Initial Analysis',
          description: 'Perform initial analysis and assessment',
          completed: false,
          completedDate: null,
          lastUpdated: new Date().toISOString().split('T')[0],
          notes: [],
          showAddNote: false,
          newNote: ''
        },
        {
          title: 'Implementation',
          description: 'Implement the proposed solution',
          completed: false,
          completedDate: null,
          lastUpdated: new Date().toISOString().split('T')[0],
          notes: [],
          showAddNote: false,
          newNote: ''
        },
        {
          title: 'Testing & Validation',
          description: 'Test and validate the implementation',
          completed: false,
          completedDate: null,
          lastUpdated: new Date().toISOString().split('T')[0],
          notes: [],
          showAddNote: false,
          newNote: ''
        }
      ];

      this.selectedTrackingItem.actionItems.push(...defaultItems);
    },

    // Add tracking update (kept for backward compatibility)
    async addTrackingUpdate() {
      try {
        if (!this.newTrackingUpdate.trim()) {
          alert('Please enter an update description');
          return;
        }

        const updateData = {
          id: this.selectedTrackingItem.id,
          update: {
            date: new Date().toISOString().split('T')[0],
            content: this.newTrackingUpdate.trim(),
            updatedBy: 'Current User' // In real implementation, get from auth
          }
        };

        // Add update to the API
        await this.addUpdate(this.selectedTrackingItem, this.newTrackingUpdate.trim());

        // Update the local data
        if (!this.selectedTrackingItem.updates) {
          this.selectedTrackingItem.updates = [];
        }
        this.selectedTrackingItem.updates.unshift(updateData.update);

        // Clear the input
        this.newTrackingUpdate = '';

        // Show success message
        alert('Update added successfully!');

      } catch (error) {
        console.error('Error adding tracking update:', error);
        alert(`Failed to add update: ${error.message}`);
      }
    },

    // Load action tracker data from API
    async loadActionTrackerData() {
      try {
        this.isLoading = true;
        this.loadingError = null;
        console.log('Loading action tracker data from API...');

        // Make API call to get action tracker data
        const response = await fetch('/api-statit2/get_action_tracker_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({})
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to fetch action tracker data: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log('Action tracker data loaded:', data);

        // Check if data has items property
        if (data.items) {
          // Format the data for display
          this.rows = data.items.map(item => ({
            id: item.id,
            commodity: item.commodity,
            group: item.group,
            pn: item.pn,
            editableTest: item.test || 'N/A',
            deadline: item.deadline || '',
            expectedResolution: item.expectedResolution || '',
            expectedImprovements: item.expectedImprovements || '',
            dateStarted: item.dateStarted || '',
            monitorDate: item.monitorDate || '',
            resolutionDate: item.resolutionDate || '',
            progress: item.progress || 0,
            priority: item.priority || 'Medium',
            source: item.source || 'manual',
            completed: item.completed || false,
            completedDate: item.completedDate || null,
            isEditingTest: false,
            isEditingDL: false,
            isEditingER: false,
            action: item.action,
            status: item.status,
            assignee: item.assignee,
            notes: item.notes || [], // Action-specific notes
            actionItems: item.actionItems || [], // Individual action items
            issues: item.issues || [],
            updates: item.updates || [],
            createdAt: item.createdAt,
            updatedAt: item.updatedAt
          }));

          for (let item of this.rows){
            
            if (!this.groupCounts[item.group]){
              this.groupCounts[item.group] = 0
              this.groupCounts[item.group] ++
            }else{
             
            this.groupCounts[item.group] ++
            }
            if (!this.updateCounts[item.deadline]){
              this.updateCounts[item.deadline] = 0
              this.updateCounts[item.deadline] ++
            }else{
             
            this.updateCounts[item.deadline] ++
            }

            if (item.priority === 'High'){
              this.priorityCounts[item.status].High ++
            }
            else if (item.priority === 'Low'){
              this.priorityCounts[item.status].Low++
            }else (this.priorityCounts[item.status].Medium++)
          }
          // Update assignee options
          this.updateAssigneeOptions();
        } else {
          this.rows = [];
          console.warn('No items found in action tracker data');
        }
      } catch (error) {
        console.error('Error loading action tracker data:', error);
        this.loadingError = error.message;
        this.rows = [];
      } finally {
        this.isLoading = false;
      }
    },

    // Save action tracker data to API
    async saveActionTrackerData(action) {
      try {
        console.log('Saving action tracker data to API:', action);

        // Make API call to save action tracker data
        const response = await fetch('/api-statit2/save_action_tracker_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': this.getAuthConfig().headers.Authorization
          },
          body: JSON.stringify(action)
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to save action tracker data: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log('Action tracker data saved:', data);

        return data;
      } catch (error) {
        console.error('Error saving action tracker data:', error);
        throw error;
      }
    },

    handleSeeMore(row) {
      this.selectedRow = row;
      this.modalVisible = true;
      console.log('See more clicked:', row);
    },

    editTest(row) {
      row.isEditingTest = true;
    },

    async stopEditingTest(row) {
      row.isEditingTest = false;
      console.log('Test name updated:', row.editableTest);

      try {
        // Update the action in the API
        await this.saveActionTrackerData({
          id: row.id,
          test: row.editableTest
        });
      } catch (error) {
        alert(`Failed to update test: ${error.message}`);
      }
    },

    editDL(row) {
      row.isEditingDL = true;
    },

    async stopEditingDL(row) {
      row.isEditingDL = false;
      console.log('Deadline updated:', row.deadline);

      try {
        // Update the action in the API
        await this.updateActionItem({
          id: row.id,
          deadline: row.deadline
        });
      } catch (error) {
        alert(`Failed to update deadline: ${error.message}`);
      }
    },

    editER(row) {
      row.isEditingER = true;
    },

    async stopEditingER(row) {
      row.isEditingER = false;
      console.log('Expected resolution updated:', row.expectedResolution);

      try {
        // Update the action in the API
        await this.updateActionItem({
          id: row.id,
          expectedResolution: row.expectedResolution
        });
      } catch (error) {
        alert(`Failed to update expected resolution: ${error.message}`);
      }
    },

    // Format date for display
    formatDate(dateString) {
      if (!dateString) return 'N/A';

      // Check if the date is in YYYY-MM-DD format
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        const [year, month, day] = dateString.split('-');
        return `${month}/${day}/${year}`;
      }

      // If it's in MM/DD/YYYY format, return as is
      return dateString;
    },

    // Update an action item
    async updateActionItem(actionData) {
      try {
        console.log('Updating action item:', actionData);

        // Make API call to update action tracker data
        const response = await fetch('/api-statit2/update_action_tracker_data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': this.getAuthConfig().headers.Authorization
          },
          body: JSON.stringify(actionData)
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to update action item: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log('Action item updated:', data);

        // Reload the action tracker data to get the updated list
        await this.loadActionTrackerData();

        return data;
      } catch (error) {
        console.error('Error updating action item:', error);
        throw error;
      }
    },

    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.slice(0, maxLength) + '...';
    },

    // Update progress for an action item
    async updateProgress(row) {
      try {
        console.log(`Updating progress for ${row.id} to ${row.progress}%`);

        // Update the action in the API
        await this.updateActionItem({
          id: row.id,
          progress: row.progress
        });

        // Add an update about the progress change
        await this.addUpdate(row, `Progress updated to ${row.progress}%`);

        // Show success message
        alert('Progress updated successfully!');
      } catch (error) {
        console.error('Error updating progress:', error);
        alert(`Failed to update progress: ${error.message}`);
      }
    },

    // Update the entire action item
    async updateEntireAction(row) {
      try {
        console.log('Updating entire action item:', row);

        // Create an update object with all editable fields
        const updateData = {
          id: row.id,
          commodity: row.commodity,
          group: row.group,
          pn: row.pn,
          test: row.editableTest,
          deadline: row.deadline,
          expectedResolution: row.expectedResolution,
          expectedImprovements: row.expectedImprovements,
          progress: row.progress,
          status: row.status,
          assignee: row.assignee,
          action: row.action,
          notes: row.notes
        };

        // Update the action in the API
        await this.updateActionItem(updateData);

        // Add an update about the action update
        await this.addUpdate(row, 'Action details updated');

        // Close the modal
        this.modalVisible = false;

        // Show success message
        alert('Action updated successfully!');
      } catch (error) {
        console.error('Error updating action:', error);
        alert(`Failed to update action: ${error.message}`);
      }
    },

    // Add an update to an action item
    async addUpdate(row, content) {
      try {
        const updateContent = content || this.newUpdate;

        if (!updateContent) {
          alert('Please enter update content');
          return;
        }

        console.log(`Adding update to ${row.id}: ${updateContent}`);

        // Make API call to add update
        const response = await fetch('/api-statit2/add_action_update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': this.getAuthConfig().headers.Authorization
          },
          body: JSON.stringify({
            id: row.id,
            update: {
              content: updateContent,
              updatedBy: 'Current User' // In a real app, this would be the logged-in user
            }
          })
        });

        // Check if response is ok
        if (!response.ok) {
          throw new Error(`Failed to add update: ${response.status} ${response.statusText}`);
        }

        // Parse response data
        const data = await response.json();
        console.log('Update added:', data);

        // Clear the update input
        this.newUpdate = '';

        // Reload the action tracker data to get the updated list
        await this.loadActionTrackerData();

        // Update the selected row with the latest data
        if (this.selectedRow) {
          const updatedRow = this.rows.find(r => r.id === this.selectedRow.id);
          if (updatedRow) {
            this.selectedRow = updatedRow;
          }
        }

        // Show success message if this was a manual update
        if (!content) {
          alert('Update added successfully!');
        }

        return data;
      } catch (error) {
        console.error('Error adding update:', error);
        alert(`Failed to add update: ${error.message}`);
        throw error;
      }
    },

    openNewActionModal() {
      // Reset the form
      this.newAction = {
        commodity: this.commodityOptions.filter(item => item !== 'All')[0] || '',
        group: '',
        pn: '',
        test: '',
        deadline: new Date().toISOString().split('T')[0],
        status: 'In-Progress',
        assignee: '',
        action: '',
        notes: ''
      };
      // Open the modal
      this.newActionModalVisible = true;
    },

      openNewAIModal() {
      // Open the modal
      this.newAIModalVisible = true;
    },

    async createNewAction() {
      // Validate required fields
      const requiredFields = ['commodity', 'group', 'action'];
      const missingFields = requiredFields.filter(field => !this.newAction[field]);

      if (missingFields.length > 0) {
        // Show error message for missing fields
        const fieldNames = missingFields.map(field => {
          switch(field) {
            case 'commodity': return 'Process/Commodity';
            case 'group': return 'Part Group';
            case 'action': return 'Action Description';
            default: return field;
          }
        });

        alert(`Please fill in the required fields: ${fieldNames.join(', ')}`);
        return;
      }

      try {
        // Ensure deadline is set
        if (!this.newAction.deadline) {
          // Set default deadline to 30 days from now if not provided
          const defaultDate = new Date();
          defaultDate.setDate(defaultDate.getDate() + 30);
          this.newAction.deadline = `${defaultDate.getFullYear()}-${String(defaultDate.getMonth() + 1).padStart(2, '0')}-${String(defaultDate.getDate()).padStart(2, '0')}`;
        }

        // Format the expected resolution date
        let formattedExpectedResolution = '';
        if (this.newAction.expectedResolution) {
          try {
            const expectedResolutionDate = new Date(this.newAction.expectedResolution);
            formattedExpectedResolution = `${expectedResolutionDate.getFullYear()}-${String(expectedResolutionDate.getMonth() + 1).padStart(2, '0')}-${String(expectedResolutionDate.getDate()).padStart(2, '0')}`;
          } catch (error) {
            console.error('Error formatting expected resolution date:', error);
            formattedExpectedResolution = this.newAction.expectedResolution; // Use as-is if there's an error
          }
        }

        // Create the new action object
        const newActionItem = {
          commodity: this.newAction.commodity,
          group: this.newAction.group,
          pn: this.newAction.pn || `${this.newAction.group}-${Date.now().toString().slice(-6)}`, // Generate a PN if not provided
          test: this.newAction.test || 'N/A',
          deadline: this.newAction.deadline,
          expectedResolution: formattedExpectedResolution,
          expectedImprovements: this.newAction.expectedImprovements || '',
          progress: this.newAction.progress || 0,
          action: this.newAction.action,
          status: this.newAction.status || 'In-Progress',
          assignee: this.newAction.assignee || 'Unassigned',
          notes: this.newAction.notes || ''
        };

        // Save the new action to the API
        const savedAction = await this.saveActionTrackerData(newActionItem);
        console.log('New action saved to API:', savedAction);

        // Reload the action tracker data to get the updated list
        await this.loadActionTrackerData();

        // Close the modal
        this.newActionModalVisible = false;

        // Show a success message
        alert('Action item created successfully!');
      } catch (error) {
        console.error('Error creating new action:', error);
        alert(`Failed to create action: ${error.message}`);
      }
    },

    // Open alert for specific action tracker item (called from PQE dashboard navigation)
    async openAlertForActionTracker(actionTrackerId) {
      try {
        // Wait for data to load
        await this.loadActionTrackerData();

        // Find the action tracker item
        const item = this.rows.find(row => row.id === actionTrackerId);
        if (item) {
          // Open the tracking modal with the alerts tab
          this.selectedTrackingItem = item;
          // this.loadPerformanceData(item);
          this.loadPerfHistoryData(item);
          this.loadAlertUpdates(item);
          // this.loadAiInsight(item);
          this.trackingModalVisible = true;

          // Switch to the alerts tab after modal opens
          this.$nextTick(() => {
            const alertsTab = document.getElementById('new-alerts-tab');
            if (alertsTab) {
              alertsTab.click();
            }
          });
        } else {
          console.error('Action tracker item not found:', actionTrackerId);
        }
      } catch (error) {
        console.error('Error opening alert for action tracker:', error);
      }
    },

    // Load alert updates for the selected item
    async loadAlertUpdates(item) {
      try {
        const response = await fetch('/api-statit2/get_pqe_alert_history', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...this.getAuthConfig().headers
          },
          body: JSON.stringify({
            actionTrackerId: item.id,
            pqeOwner: this.$route.query.pqeOwner || 'Unknown'
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch alert history: ${response.status}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          this.alertUpdates = data.alert_history || [];
        } else {
          console.error('Failed to load alert updates:', data.message);
          this.alertUpdates = [];
        }
      } catch (error) {
        console.error('Error loading alert updates:', error);
        this.alertUpdates = [];
      }
    },

    // Load AI insight for the selected item
    // async loadAiInsight(item) {
    //   this.isLoadingAiInsight = true;
    //   try {
    //     const alertData = {
    //       category: item.group || 'Unknown',
    //       severity: 'Medium', // Default severity
    //       xFactor: '1.5', // Default X-factor
    //       status: item.status || 'Unknown'
    //     };

    //     const response = await fetch('/api-statit2/get_pqe_alert_ai_insight', {
    //       method: 'POST',
    //       headers: {
    //         'Content-Type': 'application/json',
    //         ...this.getAuthConfig().headers
    //       },
    //       body: JSON.stringify({
    //         actionTrackerId: item.id,
    //         alertData: alertData,
    //         pqeOwner: this.$route.query.pqeOwner || 'Unknown'
    //       })
    //     });

    //     if (!response.ok) {
    //       throw new Error(`Failed to fetch AI insight: ${response.status}`);
    //     }

    //     const data = await response.json();
    //     if (data.status_res === 'success') {
    //       this.aiInsight = data.ai_insight || 'No insight available';
    //     } else {
    //       console.error('Failed to load AI insight:', data.message);
    //       this.aiInsight = 'Unable to generate insight at this time';
    //     }
    //   } catch (error) {
    //     console.error('Error loading AI insight:', error);
    //     this.aiInsight = 'Unable to generate insight at this time';
    //   } finally {
    //     this.isLoadingAiInsight = false;
    //   }
    // },

    // Add alert update
    async addAlertUpdate() {
      if (!this.newAlertUpdate.trim()) {
        alert('Please enter an update');
        return;
      }

      try {
        const response = await fetch('/api-statit2/add_pqe_alert_update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...this.getAuthConfig().headers
          },
          body: JSON.stringify({
            actionTrackerId: this.selectedTrackingItem.id,
            update: this.newAlertUpdate,
            updatedBy: this.$route.query.pqeOwner || 'Unknown',
            alertType: 'PQE Update'
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to add alert update: ${response.status}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          // Add the new alert to the history
          this.alertUpdates.push(data.alert);
          this.newAlertUpdate = '';
          console.log('Alert update added successfully');
        } else {
          throw new Error(data.message || 'Failed to add alert update');
        }
      } catch (error) {
        console.error('Error adding alert update:', error);
        alert('Failed to add alert update: ' + error.message);
      }
    }
  },
};

</script>

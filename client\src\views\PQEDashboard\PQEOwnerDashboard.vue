<template>
    <div class="pqe-owner-dashboard-container">
      <div class="content-wrapper">
        <!-- Header Section with Critical Issues Summary -->
        <div class="dashboard-header">
          <h3>{{ pqeOwner }}'s Dashboard</h3>
          <div class="last-updated-text">
            Last Updated: {{ new Date().toLocaleString() }}
          </div>
        </div>

        <!-- Key Metrics Section -->
         <div>What do you want to do?</div>
        <div class="key-metrics-section">
          <cv-tile 
            class="metric-tile enhanced-tile" 
            @click="scrollToAlerts" 
            title="Click to go to alerts section"
          >
            <div class="metric-content">
              <div class="metric-icon alerts">
                <warning-alt20 class="metric-icon-svg" />
              </div>
              <div class="metric-details">
                <div class="metric-label"># Alerts</div>
                <div class="metric-value">{{ totalAlerts }}</div>
                <div class="metric-description">Current Month</div>
              </div>
            </div>
          </cv-tile>

          <cv-tile 
            class="metric-tile enhanced-tile" 
            @click="goToActionTracker" 
            title="Click to go to Action Tracker"
          >
            <div class="metric-content">
              <div class="metric-icon in-progress">
                <in-progress20 class="metric-icon-svg" />
              </div>
              <div class="metric-details">
                <div class="metric-label"># In Progress Issues</div>
                <div class="metric-value">{{ inProgressIssuesCount }}</div>
                <div class="metric-description">From Action Tracker</div>
              </div>
            </div>
          </cv-tile>

          <cv-tile 
            class="metric-tile enhanced-tile" 
            @click="goToValidationPage" 
            title="Click to go to Validation page"
          >
            <div class="metric-content">
              <div class="metric-icon validated">
                <checkmark-outline20 class="metric-icon-svg" />
              </div>
              <div class="metric-details">
                <div class="metric-label">Validated Fails</div>
                <div class="metric-value">{{ validatedCount }}/{{ totalFails }}</div>
                <div class="metric-description">This Month</div>
              </div>
            </div>
          </cv-tile>

          <cv-tile 
            class="metric-tile enhanced-tile" 
            kind = "clickable"
            @click="goToHeatmap" 
            title="Click to go to Heatmap"
          >
            <div class="metric-content">
              <div class="metric-icon groups-over-target">
                <chart-column20 class="metric-icon-svg" />
              </div>
              <div class="metric-details">
                <div class="metric-label"># Groups Over Target</div>
                <div class="metric-value">{{ groupsOverTargetCount }}</div>
                <div class="metric-description">Current Month</div>
              </div>
            </div>
          </cv-tile>
        </div>

        <!-- Main Content Layout -->
        <div class="dashboard-main-content">
          <!-- Main Dashboard Accordion -->
          <cv-accordion class="dashboard-accordion">
            <!-- Action Tracker Alerts Section -->
            <cv-accordion-item :open="isActionTrackerAlertsExpanded" @change="toggleActionTrackerAlertsExpanded">
              <template v-slot:title>
                <div class="accordion-title-container">
                    <h4 class="accordion-title">Action Tracker Alerts</h4>
                  <div class="accordion-badge" :class="{ 'flashing': totalActionTrackerAlerts > 0 }">
                    {{ totalActionTrackerAlerts }}
                  </div>
                </div>
              </template>
              <template v-slot:content>
              <!-- Action Tracker Alerts Data Table -->
              <cv-data-table
                :columns="actionTrackerColumns"
                :title="''"
                :expandable="true"
              >
                <template slot="data">
                  <!-- In-Progress Alerts -->
                  <cv-data-table-row
                    v-for="alert in inProgressAlerts"
                    :key="'in-progress-' + alert.id"
                    :expandable="true"
                  >
                    <cv-data-table-cell>
                      <cv-tag kind="teal" label="In-Progress" />
                    </cv-data-table-cell>
                    <cv-data-table-cell>{{ alert.group }}</cv-data-table-cell>
                    <cv-data-table-cell>{{ alert.severity }}</cv-data-table-cell>
                    <!-- <cv-data-table-cell>
                      <span class="issue-multiplier medium-severity">{{ alert.xFactor }}x</span>
                    </cv-data-table-cell> -->
                    <!-- <cv-data-table-cell>
                      <cv-button
                        kind="primary"
                        size="small"
                        @click.stop="viewActionTrackerAlert(alert)"
                      >
                        View
                      </cv-button>
                    </cv-data-table-cell> -->

                    <template slot="expandedContent">
                      <div class="expanded-content">
                        <div class="expanded-description">
                          <p>{{ alert.description || 'No additional details available.' }}</p>
                        </div>
                        <div class="expanded-actions">
                          <cv-button
                            kind="primary"
                            size="small"
                            @click="viewActionTrackerAlert(alert)"
                          >
                            View Details
                          </cv-button>
                          <cv-button
                            kind="tertiary"
                            size="small"
                            @click="goToActionTracker"
                          >
                            Go to Action Tracker
                          </cv-button>
                        </div>
                      </div>
                    </template>
                  </cv-data-table-row>

                  <!-- Monitored Alerts -->
                  <cv-data-table-row
                    v-for="alert in monitoredAlerts"
                    :key="'monitored-' + alert.id"
                    :expandable="true"
                  >
                    <cv-data-table-cell>
                      <cv-tag kind="grey" label="Monitored" />
                    </cv-data-table-cell>
                    <cv-data-table-cell>{{ alert.group }}</cv-data-table-cell>
                    <cv-data-table-cell>{{ alert.severity }}</cv-data-table-cell>
                    <!-- <cv-data-table-cell>
                      <span class="issue-multiplier medium-severity">{{ alert.xFactor }}x</span>
                    </cv-data-table-cell> -->
                    <!-- <cv-data-table-cell>
                      <cv-button
                        kind="primary"
                        size="small"
                        @click.stop="viewActionTrackerAlert(alert)"
                      >
                        View
                      </cv-button>
                    </cv-data-table-cell> -->

                    <template slot="expandedContent">
                      <div class="expanded-content">
                        <div class="expanded-description">
                          <p>{{ alert.description || 'No additional details available.' }}</p>
                        </div>
                        <div class="expanded-actions">
                          <cv-button
                            kind="tertiary"
                            size="small"
                            @click="viewActionTrackerAlert(alert)"
                          >
                            View Details
                          </cv-button>
                          <cv-button
                            kind="ghost"
                            size="small"
                            @click="goToActionTracker"
                          >
                            Go to Action Tracker
                          </cv-button>
                        </div>
                      </div>
                    </template>
                  </cv-data-table-row>

                  <!-- Resolved Alerts -->
                  <cv-data-table-row
                    v-for="alert in resolvedAlerts"
                    :key="'resolved-' + alert.id"
                    :expandable="true"
                  >
                    <cv-data-table-cell>
                      <cv-tag kind="green" label="Resolved" />
                    </cv-data-table-cell>
                    <cv-data-table-cell>{{ alert.group }}</cv-data-table-cell>
                    <cv-data-table-cell>{{ alert.severity }}</cv-data-table-cell>
                    <!-- <cv-data-table-cell>
                      <span class="issue-multiplier good-performance">{{ alert.xFactor }}x</span>
                    </cv-data-table-cell> -->
                    <!-- <cv-data-table-cell>
                      <cv-button
                        kind="primary"
                        size="small"
                        @click.stop="viewActionTrackerAlert(alert)"
                      >
                        View
                      </cv-button>
                    </cv-data-table-cell> -->

                    <template slot="expandedContent">
                      <div class="expanded-content">
                        <div class="expanded-description">
                          <p>{{ alert.description || 'No additional details available.' }}</p>
                        </div>
                        <div class="expanded-actions">
                          <cv-button
                            kind="tertiary"
                            size="small"
                            @click="viewActionTrackerAlert(alert)"
                          >
                            View Details
                          </cv-button>
                          <cv-button
                            kind="ghost"
                            size="small"
                            @click="goToActionTracker"
                          >
                            Go to Action Tracker
                          </cv-button>
                        </div>
                      </div>
                    </template>
                  </cv-data-table-row>

                  <!-- Empty state -->
                  <cv-data-table-row v-if="totalActionTrackerAlerts === 0">
                    <cv-data-table-cell :colspan="5">
                      <div class="no-data-message">
                        No action tracker alerts found.
                      </div>
                    </cv-data-table-cell>
                  </cv-data-table-row>
                </template>
              </cv-data-table>
            </template>
            </cv-accordion-item>

            <!-- Critical Issues Section -->
            <cv-accordion-item :open="isCriticalIssuesExpanded" @change="toggleCriticalIssuesExpanded">
              <template v-slot:title>
                <div class="accordion-title-container">
                    <h4 class="accordion-title">Critical Issues</h4>
                  <div class="accordion-badge critical-issues-badge" :class="{ 'flashing': !isCriticalIssuesExpanded && unresolvedCriticalIssues.length > 0 }">
                    {{ unresolvedCriticalIssues.length }}
                  </div>
                </div>
              </template>
              <template v-slot:content>
                <!-- Filter Controls in a Single Row -->
                <div class="filter-container">
                  <div class="filter-header">
                    <h5 class="filter-title">Filter Issues</h5>
                    <cv-button
                      kind="ghost"
                      size="small"
                      class="clear-filters-button"
                      @click="clearFilters"
                      v-if="isFiltersActive"
                    >
                      Clear Filters
                    </cv-button>
                  </div>

                  <div class="filter-controls">
                    <div class="filter-group">
                      <label for="severity-dropdown" class="filter-label">Severity:</label>
                      <cv-dropdown
                        id="severity-dropdown"
                        v-model="severityFilter"
                        @change="handleSeverityFilterChange"
                        class="filter-dropdown"
                      >
                        <cv-dropdown-item value="all">All Severities</cv-dropdown-item>
                        <cv-dropdown-item value="high">High</cv-dropdown-item>
                        <cv-dropdown-item value="medium">Medium</cv-dropdown-item>
                      </cv-dropdown>
                    </div>

                    <div class="filter-group">
                      <label for="analysis-dropdown" class="filter-label">Analysis Type:</label>
                      <cv-dropdown
                        id="analysis-dropdown"
                        v-model="analysisTypeFilter"
                        @change="handleAnalysisFilterChange"
                        class="filter-dropdown"
                      >
                        <cv-dropdown-item value="all">All Analysis Types</cv-dropdown-item>
                        <cv-dropdown-item value="Root Cause">Root Cause</cv-dropdown-item>
                      </cv-dropdown>
                    </div>
                  </div>
                </div>

                <!-- Critical Issues Data Table -->
                <cv-data-table
                  :columns="criticalIssuesColumns"
                  :title="''"
                  :expandable="true"
                >
                  <template slot="data">
                    <cv-data-table-row
                      v-for="issue in filteredCriticalIssues"
                      :key="issue.id"
                      :expandable="true"
                    >
                      <cv-data-table-cell>
                        <cv-tag
                          :kind="issue.severity === 'high' ? 'red' : 'magenta'"
                          :label="issue.severity === 'high' ? 'High' : 'Medium'"
                        />
                      </cv-data-table-cell>
                      <cv-data-table-cell>{{ issue.category }}</cv-data-table-cell>
                      <cv-data-table-cell>{{ issue.analysisType }}</cv-data-table-cell>
                      <cv-data-table-cell>{{ issue.month }}</cv-data-table-cell>
                      <cv-data-table-cell>
                        <span class="issue-multiplier" :class="issue.severity === 'high' ? 'high-severity' : 'medium-severity'">
                          {{ issue.increaseMultiplier === '(new)' ? '(new)' : `${issue.increaseMultiplier}x` }}
                        </span>
                      </cv-data-table-cell>

                      <template slot="expandedContent">
                        <div class="expanded-content">
                          <!-- AI Description Section -->
                          <div class="expanded-section">
                            <h5 class="section-title">AI Analysis</h5>
                            <div class="ai-description">
                              <p>{{ issue.aiDescription || 'Loading AI description...' }}</p>
                            </div>
                          </div>

                          <!-- Action Comment Section -->
                          <div class="expanded-section">
                            <h5 class="section-title">Action Comments</h5>
                            <div class="action-comment">
                              <cv-text-area
                                v-model="issue.comment"
                                label=""
                                placeholder="Add your comments or action plan here..."
                                :helper-text="issue.comment ? `${issue.comment.length} characters` : 'This will be added to the Action Tracker'"
                                rows="3"
                              ></cv-text-area>
                            </div>
                          </div>

                          <!-- Action Buttons Section -->
                          <div class="expanded-section">
                            <h5 class="section-title">Actions</h5>
                            <div class="issue-actions">
                              <div class="action-group primary-actions">
                                <cv-button
                                  kind="tertiary"
                                  size="small"
                                  @click="viewIssueDetails(issue)"
                                >
                                  View Data
                                </cv-button>
                                <cv-button
                                  kind="tertiary"
                                  size="small"
                                  @click="updateIssue(issue)"
                                >
                                  Update
                                </cv-button>
                              </div>
                              <div class="action-group status-actions">
                                <cv-button
                                  kind="secondary"
                                  size="small"
                                  @click="updateIssue(issue, false, true)"
                                >
                                  Mark Outstanding
                                </cv-button>
                                <cv-button
                                  kind="primary"
                                  size="small"
                                  @click="updateIssue(issue, true, false)"
                                >
                                  Mark Resolved
                                </cv-button>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </cv-data-table-row>

                    <cv-data-table-row v-if="filteredCriticalIssues.length === 0">
                      <cv-data-table-cell :colspan="5">
                        <div class="no-data-message">
                          No critical issues found matching the selected filters.
                        </div>
                      </cv-data-table-cell>
                    </cv-data-table-row>
                  </template>
                </cv-data-table>
              </template>
              </cv-accordion-item>

            <!-- Analysis Section -->
            <cv-accordion-item :open="isAnalysisSectionExpanded" @change="toggleAnalysisSectionExpanded">
              <template slot="title">
                <div class="accordion-title-container">
                  
                    <h4 class="accordion-title">Analysis</h4>
                </div>
              </template>
              <template v-slot:content>

              <div class="chart-controls">
                <div class="control-group">
                  <label class="control-label">View By:</label>
                  <cv-dropdown
                    v-model="rootCauseViewBy"
                    @change="handleRootCauseViewByChange"
                    class="control-dropdown"
                  >
                    <cv-dropdown-item value="rootCause">Root Cause</cv-dropdown-item>
                    <cv-dropdown-item value="vintage">Vintage</cv-dropdown-item>
                    <cv-dropdown-item value="sector">Sector</cv-dropdown-item>
                    <cv-dropdown-item value="supplier">Supplier</cv-dropdown-item>
                    <cv-dropdown-item value="partNum">Part Number</cv-dropdown-item>
                  </cv-dropdown>
                </div>

                <div class="control-group">
                  <label class="control-label">Time Range:</label>
                  <cv-dropdown
                    v-model="rootCauseTimeRange"
                    @change="handleRootCauseTimeRangeChange"
                    class="control-dropdown"
                  >
                    <cv-dropdown-item value="3month">3 Months</cv-dropdown-item>
                    <cv-dropdown-item value="6month">6 Months</cv-dropdown-item>
                  </cv-dropdown>
                </div>

                <div class="control-group" v-if="breakoutGroups.length > 0">
                  <label class="control-label">Group:</label>
                  <cv-dropdown
                    v-model="rootCauseSelectedGroup"
                    @change="handleRootCauseGroupChange"
                    class="control-dropdown"
                  >
                    <cv-dropdown-item value="all">All Groups</cv-dropdown-item>
                    <cv-dropdown-item
                      v-for="group in breakoutGroups"
                      :key="group.name"
                      :value="group.name"
                    >
                      {{ group.name }}
                    </cv-dropdown-item>
                  </cv-dropdown>
                </div>
              </div>

              <div class="chart-container">
                <div v-if="isRootCauseDataLoading" >
                          Loading Chart...
                          <RootCauseChart :data = [] :loading="isRootCauseDataLoading"/>
                        </div>
                <RootCauseChart
                 v-if="rootCauseChartData.length > 0 && !isRootCauseDataLoading"
                  :data="rootCauseChartData"
                  :viewBy="rootCauseViewBy"
                  :timeRange="rootCauseTimeRange"
                  :selectedGroup="rootCauseSelectedGroup"
                  :loading="isRootCauseDataLoading"
                  :title = "rootCauseTitle"
                  @bar-click="handleRootCauseBarClick"
                />

                <div v-if="rootCauseChartData.length == 0 && !isRootCauseDataLoading" >
                          No data available
                        </div>
                <!-- <div v-else-if="isRootCauseDataLoading" class="loading-container">
                  <p>Loading root cause data...</p>
                </div>
                <div v-else class="no-data-message">
                  No root cause data available for the selected criteria.
                </div> -->
              </div>

              <!-- <div class="section-footer">
                <p class="section-description">
                  Root cause analysis showing defect categories and their fail rates over time.
                  Click on bars to see detailed information.
                </p>
              </div> -->
            </template>
              </cv-accordion-item>
          </cv-accordion>
        </div>
      </div>
      

  <!-- Action Tracker Modal (same as Action Tracker page) -->
  <cv-modal
    class="tracking-modal"
    :visible="trackingModalVisible"
    @modal-hidden="trackingModalVisible = false"
    :size="'xl'"
  >
    <template slot="title">
      <div>Action Tracking - {{ selectedTrackingItem ? selectedTrackingItem.pn : '' }}</div>
    </template>
    <template slot="content">
      <div class="tracking-modal-content" v-if="selectedTrackingItem">
        <!-- Tracking Tabs -->
        <cv-tabs>
          <cv-tab id="new-alerts-tab" label="New Alerts">
            <div class="tracking-tab-content">
              <div class="new-alerts-section">
                <h3 class="section-title">Alert Management</h3>
                <p class="section-description">Manage alerts and updates for {{ selectedTrackingItem.pn }}</p>

                <!-- AI Insight Section -->
                <div class="ai-insight-section">
                  <h4 class="subsection-title">AI Insight</h4>
                  <div v-if="isLoadingAiInsight" class="loading-message">
                    Generating AI insight...
                  </div>
                  <div v-else class="ai-insight-content">
                    {{ aiInsight || 'No AI insight available for this alert.' }}
                  </div>
                </div>

                <!-- Add New Alert Update -->
                <div class="add-alert-section">
                  <h4 class="subsection-title">Add Alert Update</h4>
                  <div class="add-update-form">
                    <cv-text-area
                      v-model="newAlertUpdate"
                      label="Update Content"
                      placeholder="Enter update details..."
                      rows="4"
                    ></cv-text-area>
                    <div class="update-form-actions">
                      <cv-button
                        kind="primary"
                        @click="addAlertUpdate"
                        :disabled="!newAlertUpdate.trim()"
                      >
                        Add Update
                      </cv-button>
                    </div>
                  </div>
                </div>

                <!-- Alert History Table -->
                <div class="alert-updates-section">
                  <h4 class="subsection-title">Alert History</h4>
                  <div v-if="alertUpdates.length > 0" class="alert-updates-table">
                    <div class="updates-header">
                      <div class="update-column">Date</div>
                      <div class="update-column">Update</div>
                      <div class="update-column">Updated By</div>
                    </div>
                    <div
                      v-for="(update, index) in alertUpdates"
                      :key="index"
                      class="update-row"
                    >
                      <div class="update-cell">{{ update.date }}</div>
                      <div class="update-cell">{{ update.update }}</div>
                      <div class="update-cell">{{ update.updatedBy }}</div>
                    </div>
                  </div>
                  <div v-else class="no-updates-message">
                    No alert updates available.
                  </div>
                </div>
              </div>
            </div>
          </cv-tab>

          <cv-tab id="action-items-tab" label="Action Items">
            <div class="tracking-tab-content">
              <div class="tracking-section">
                <h3 class="tracking-section-title">Action Details</h3>
                <div class="action-summary">
                  <div class="summary-item">
                    <span class="summary-label">Part Group:</span>
                    <span class="summary-value">{{ selectedTrackingItem.group }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">Part Number:</span>
                    <span class="summary-value">{{ selectedTrackingItem.pn }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">Status:</span>
                    <span class="summary-value">{{ selectedTrackingItem.status }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">Priority:</span>
                    <span class="summary-value priority-badge" :class="selectedTrackingItem.priority ? selectedTrackingItem.priority.toLowerCase() : 'medium'">
                      {{ selectedTrackingItem.priority || 'Medium' }}
                    </span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">Progress:</span>
                    <span class="summary-value">{{ selectedTrackingItem.progress || 0 }}%</span>
                  </div>
                </div>
              </div>

              <div class="tracking-section">
                <h3 class="tracking-section-title">Action Items & Progress</h3>
                <div class="action-items-list">
                  <div
                    v-for="(actionItem, index) in selectedTrackingItem.actionItems"
                    :key="index"
                    class="action-item-card"
                    :class="{ 'completed': actionItem.completed }"
                  >
                    <!-- Action Item Header -->
                    <div class="action-item-header">
                      <cv-checkbox
                        v-model="actionItem.completed"
                        :value="actionItem.completed"
                        :label="actionItem.title"
                        @change="updateActionItemCompletion(actionItem, index)"
                      />
                      <span class="completion-date" v-if="actionItem.completed && actionItem.completedDate">
                        Completed on {{ formatDate(actionItem.completedDate) }}
                      </span>
                      <span class="last-updated" v-if="actionItem.lastUpdated">
                        Last updated: {{ formatDate(actionItem.lastUpdated) }}
                      </span>
                    </div>

                    <!-- Action Item Description -->
                    <div class="action-item-description" v-if="actionItem.description">
                      {{ actionItem.description }}
                    </div>
                  </div>

                  <!-- No Action Items -->
                  <div v-if="!selectedTrackingItem.actionItems || selectedTrackingItem.actionItems.length === 0" class="no-action-items">
                    <p>No action items defined for this tracking item.</p>
                    <cv-button
                      kind="primary"
                      size="small"
                      @click="addDefaultActionItems"
                    >
                      Create Default Action Items
                    </cv-button>
                  </div>
                </div>
              </div>
            </div>
          </cv-tab>

          <cv-tab id="performance-tab" label="Performance Chart & History">
            <div class="tracking-tab-content">
              <!-- Performance Chart Section -->
              <div class="chart-section">
                <h3 class="section-title">Performance Chart</h3>
                <LineChart :data="perfChartData"
                        :options="chartOptions"
                        :loading="isLoading" />
              </div>

              <!-- Performance History Table Section -->
              <div class="performance-history-section">
                <h3 class="section-title">Monthly Performance History</h3>
                <p class="section-description">Historical record of performance and status for {{ selectedTrackingItem.pn }}</p>

                <cv-data-table
                  :columns="alertHistoryColumns"
                  :title="''"
                  class="alert-history-table"
                >
                  <template slot="data">
                    <cv-data-table-row
                      v-for="(record, index) in alertHistoryData"
                      :key="index"
                      :class="getAlertRowClass(record)"
                    >
                      <cv-data-table-cell>{{ record.month }}</cv-data-table-cell>
                      <cv-data-table-cell>{{ record.year }}</cv-data-table-cell>
                      <cv-data-table-cell>
                        <div class="status-cell">
                          <span class="status-indicator" :class="record.status.toLowerCase()"></span>
                          <span class="status-text" :class="record.status.toLowerCase()">{{ record.status }}</span>
                        </div>
                      </cv-data-table-cell>
                      <cv-data-table-cell>{{ record.actualRate }}%</cv-data-table-cell>
                      <cv-data-table-cell>{{ record.targetRate }}%</cv-data-table-cell>
                      <cv-data-table-cell>{{ record.xFactor }}</cv-data-table-cell>
                      <cv-data-table-cell>{{ record.volume }}</cv-data-table-cell>
                      <cv-data-table-cell>{{ record.defects }}</cv-data-table-cell>
                      <cv-data-table-cell>{{ record.notes || 'N/A' }}</cv-data-table-cell>
                    </cv-data-table-row>
                  </template>
                </cv-data-table>

                <div v-if="!alertHistoryData || alertHistoryData.length === 0" class="no-alert-data">
                  <p>No performance history available for this part</p>
                  <p class="note">Performance history will be populated as data becomes available</p>
                </div>
              </div>
            </div>
          </cv-tab>
        </cv-tabs>
      </div>
    </template>
  </cv-modal>
</div>
</template>


 
<script>

import { 
  // WarningAlt20, 
  // InProgress20, 
  // CheckmarkOutline20, 
  // ChartColumn20,
  // ArrowRight16,
  // Information16
} from '@carbon/icons-vue';

import RootCauseChart from '@/components/RootCauseChart/RootCauseChart';
// import PerformanceChart from '@/components/PerformanceChart/PerformanceChart.vue';
import LineChart from '@/components/LineChart/LineChart.vue';


export default {
  name: 'PQEOwnerDashboard',
  components: {

    // WarningAlt20,
    // InProgress20,
    // CheckmarkOutline20,
    // ChartColumn20,
    // ArrowRight16,
    // Information16,
    RootCauseChart,
    LineChart
  },
  props: {
    pqeOwner: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      // Critical Issues Data
      newCriticalIssues: [],
      unresolvedCriticalIssues: [],
      expandedIssueIds: [], // Track which issues are expanded
      isCriticalIssuesExpanded: false, // Track if critical issues section is expanded

      // Critical Issues Table Configuration
      criticalIssuesColumns: [
        { label: 'Severity', value: 'severity' },
        { label: 'Category/Issue', value: 'category' },
        { label: 'Analysis Type', value: 'analysisType' },
        { label: 'Month', value: 'month' },
        { label: 'Impact', value: 'increaseMultiplier' }
      ],

      // Action Tracker Alerts Data
      isActionTrackerAlertsExpanded: false,
      
      // Action Tracker Alert Data
      inProgressAlerts: [],
      monitoredAlerts: [],
      resolvedAlerts: [],

      // Action Tracker Table Configuration
      actionTrackerColumns: [
        { label: 'Status', value: 'status' },
        { label: 'Group', value: 'group' },
        { label: 'Severity', value: 'severity' },
      ],

      // Filter Data
      selectedFilters: {
        severity: [],
        analysisType: []
      },
      severityFilter: 'all',
      analysisTypeFilter: 'all',

      // Validation Data
      validatedCount: 0,
      unvalidatedCount: 0,

      // Breakout Groups Data
      breakoutGroups: [],

      // Root Cause Chart Data
      rootCauseChartData: [],
      rootCauseTitle: '',
      isRootCauseDataLoading: true,

      // Root Cause Chart Controls
      rootCauseViewBy: 'rootCause',
      rootCauseTimeRange: '6month',
      rootCauseSelectedGroup: 'all',

      // Loading States
      isLoading: false,

      // Action Tracker Modal Data
      trackingModalVisible: false,
      selectedTrackingItem: null,
      alertUpdates: [],
      newAlertUpdate: '',
      aiInsight: '',
      isLoadingAiInsight: false,
      alertHistoryData: [],
      alertHistoryColumns: ['Month', 'Year', 'Status', 'Actual Rate', 'Target Rate', 'X-Factor', 'Volume', 'Defects', 'Notes'],

      // New Critical Issue Modal Data
      criticalIssueModalVisible: false,
      selectedCriticalIssue: null,
      criticalIssueUpdates: [],
      newCriticalIssueUpdate: '',
      isLoadingCriticalIssueUpdates: false
    };
  },
  computed: {
    // Filtered critical issues based on selected filters
    filteredCriticalIssues() {
      // If no filters are selected, return all issues
      if (this.selectedFilters.severity.length === 0 && this.selectedFilters.analysisType.length === 0) {
        return this.unresolvedCriticalIssues;
      }

      // Apply filters
      return this.unresolvedCriticalIssues.filter(issue => {
        // Check if issue passes severity filter
        const passesSeverityFilter = this.selectedFilters.severity.length === 0 ||
                                    this.selectedFilters.severity.includes(issue.severity);

        // Check if issue passes analysis type filter
        const passesAnalysisFilter = this.selectedFilters.analysisType.length === 0 ||
                                    this.selectedFilters.analysisType.includes(issue.analysisType);

        // Issue must pass both filters
        return passesSeverityFilter && passesAnalysisFilter;
      });
    },

    // Computed property for expanded critical issues to avoid v-if inside v-for
    expandedCriticalIssues() {
      return this.filteredCriticalIssues.filter(issue => this.expandedIssueIds.includes(issue.id));
    },

    // Check if any filters are active
    isFiltersActive() {
      return this.selectedFilters.severity.length > 0 || this.selectedFilters.analysisType.length > 0;
    },



    // Calculate total alerts (critical breakout groups)
    totalAlerts() {
      return this.breakoutGroups.filter(group => group.xFactor >= 1.5).length;
    },

    // Calculate total in-progress issues from action tracker
    inProgressIssuesCount() {
      // This would normally fetch from action tracker API
      // For now, return a calculated value based on action tracker alerts
      return this.inProgressAlerts.length;
    },

    // Calculate total fails (validated + unvalidated)
    totalFails() {
      return this.validatedCount + this.unvalidatedCount;
    },

    // Calculate groups over target (xFactor > 1.0)
    groupsOverTargetCount() {
      return this.breakoutGroups.filter(group => group.xFactor > 1.0).length;
    },

    // Calculate total action tracker alerts
    totalActionTrackerAlerts() {
      return this.inProgressAlerts.length + this.monitoredAlerts.length + this.resolvedAlerts.length;
    }
  },
  watch: {
    pqeOwner: {
      immediate: true,
      handler(newValue, oldValue) {
        console.log(`PQE Owner changed from ${oldValue} to ${newValue}`);
        if (newValue) {
          // Reset the group selection when PQE owner changes
          this.rootCauseSelectedGroup = 'all';
          this.loadDashboardData();
        }
      }
    }
  },
    methods: {
    loadDashboardData() {
      this.loadCriticalIssues();
      this.loadValidationCounts();
      this.loadActionTrackerAlerts();
      this.loadRootCauseData();
    },

    // Open critical issue modal
    openCriticalIssueModal(issue) {
      this.selectedCriticalIssue = issue;
      this.criticalIssueModalVisible = true;
      this.newCriticalIssueUpdate = '';
      this.loadCriticalIssueUpdates(issue);
    },

    // Close critical issue modal
    closeCriticalIssueModal() {
      this.criticalIssueModalVisible = false;
      this.selectedCriticalIssue = null;
      this.criticalIssueUpdates = [];
      this.newCriticalIssueUpdate = '';
    },

    // Load updates for critical issue (mock or API)
    async loadCriticalIssueUpdates() {
      this.isLoadingCriticalIssueUpdates = true;
      try {
        // For now, mock data - replace with API call if available
        // Example API call could be similar to loadAlertUpdates
        // const response = await fetch('/api-statit2/get_critical_issue_updates', { ... })

        // Mock data
        this.criticalIssueUpdates = [
          {
            date: '2024-06-01',
            update: 'Initial detection of issue.',
            updatedBy: 'System'
          },
          {
            date: '2024-06-05',
            update: 'PQE team started investigation.',
            updatedBy: 'PQE User'
          }
        ];
      } catch (error) {
        console.error('Error loading critical issue updates:', error);
        this.criticalIssueUpdates = [];
      } finally {
        this.isLoadingCriticalIssueUpdates = false;
      }
    },

    // Add update to critical issue
    async addCriticalIssueUpdate() {
      if (!this.newCriticalIssueUpdate.trim()) {
        alert('Please enter an update');
        return;
      }

      try {
        // In real implementation, send update to backend API
        // For now, just add to local array
        const newUpdate = {
          date: new Date().toISOString().split('T')[0],
          update: this.newCriticalIssueUpdate.trim(),
          updatedBy: this.pqeOwner
        };
        this.criticalIssueUpdates.push(newUpdate);
        this.newCriticalIssueUpdate = '';
        alert('Update added successfully');
      } catch (error) {
        console.error('Error adding critical issue update:', error);
        alert('Failed to add update: ' + error.message);
      }
    },





    async loadCriticalIssues() {
      console.log(`Loading critical issues for PQE owner: ${this.pqeOwner}`);
      this.isLoading = true;

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // First, load the breakout groups for this PQE owner
        await this.loadBreakoutGroups();

        // Fetch critical issues from the API
        const response = await fetch('/api-statit2/get_pqe_critical_issues', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch critical issues: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Filter critical issues to only include those related to this PQE's breakout groups
          const allIssues = data.critical_issues || [];

          // If we have breakout groups, filter issues to only include those for this PQE's groups
          if (this.breakoutGroups.length > 0) {
            this.unresolvedCriticalIssues = allIssues.filter(issue => {
              // Check if the issue's category matches any of the PQE's breakout groups
              return this.breakoutGroups.some(group =>
                issue.category.includes(group.name) || group.name.includes(issue.category)
              );
            });
          } else {
            // If we don't have breakout groups yet, use all issues
            this.unresolvedCriticalIssues = allIssues;
          }

          console.log(`Loaded ${this.unresolvedCriticalIssues.length} critical issues for ${this.pqeOwner}'s groups`);
        } else {
          console.error('Failed to load critical issues:', data.message);
          // Use sample data for development
          this.loadSampleCriticalIssues();
        }
      } catch (error) {
        console.error('Error loading critical issues:', error);
        // Use sample data for development
        this.loadSampleCriticalIssues();
      } finally {
        this.isLoading = false;
      }
    },

    async loadBreakoutGroups() {
      console.log(`Loading breakout groups for PQE owner: ${this.pqeOwner}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch breakout groups from the API
        const response = await fetch('/api-statit2/get_pqe_breakout_groups', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch breakout groups: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.breakoutGroups = data.breakout_groups || [];
          console.log(`Loaded ${this.breakoutGroups.length} breakout groups for ${this.pqeOwner}`);

          // Reload root cause chart data now that we have the correct breakout groups
          await this.loadRootCauseData();
        } else {
          console.error('Failed to load breakout groups:', data.message);
          // Use sample data for development
          this.loadSampleBreakoutGroups();
          // Reload root cause chart data with sample groups
          await this.loadRootCauseData();
        }
      } catch (error) {
        console.error('Error loading breakout groups:', error);
        // Use sample data for development
        this.loadSampleBreakoutGroups();
        // Reload root cause chart data with sample groups
        await this.loadRootCauseData();
      }
    },

    async loadSampleBreakoutGroups() {
      // Sample data for development
      if (this.pqeOwner === 'Albert G.') {
        this.breakoutGroups = [
          { name: 'Fan Themis', status: 'Short-Term Spike', xFactor: 3.2 },
          { name: 'Victoria Crypto', status: 'Sustained Problem', xFactor: 1.8 },
          { name: 'Quantum Nexus', status: 'Sustained Problem', xFactor: 1.6 }
        ];
      } else if (this.pqeOwner === 'Sarah L.') {
        this.breakoutGroups = [
          { name: 'Stellar Core', status: 'Short-Term Spike', xFactor: 2.5 },
          { name: 'Nebula Drive', status: 'Sustained Problem', xFactor: 1.7 }
        ];
      } else {
        // Default sample data
        this.breakoutGroups = [
          { name: 'Sample Group 1', status: 'Normal', xFactor: 0.9 },
          { name: 'Sample Group 2', status: 'Normal', xFactor: 0.8 }
        ];
      }

      // Reload root cause chart data with the sample groups
      await this.loadRootCauseData();
    },

    loadSampleCriticalIssues() {
      // Sample data for development
      this.unresolvedCriticalIssues = [
        {
          id: 'ci1',
          category: 'Fan Themis',
          month: '2024-06',
          severity: 'high',
          increaseMultiplier: '3.2',
          aiDescription: 'Fan Themis showing 3.2x spike in June 2024 with 12 fails vs 4 expected. Root cause appears to be airflow issues in the new design.',
          comment: '',
          analysisType: 'Root Cause'
        },
        {
          id: 'ci2',
          category: 'Victoria Crypto',
          month: '2024-06',
          severity: 'medium',
          increaseMultiplier: '1.8',
          aiDescription: 'Victoria Crypto has sustained problem with 1.8x target rate for 3 consecutive months. Consistent pattern of power delivery issues.',
          comment: '',
          analysisType: 'Root Cause'
        },

      ];

      // Set new critical issues to empty array for now
      this.newCriticalIssues = [];
    },

    async loadValidationCounts() {
      console.log(`Loading validation counts for PQE owner: ${this.pqeOwner}`);

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Fetch validation counts from the API
        const response = await fetch('/api-statit2/get_validation_counts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch validation counts: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          this.validatedCount = data.validated_count || 0;
          this.unvalidatedCount = data.unvalidated_count || 0;
          console.log(`Loaded validation counts: ${this.validatedCount} validated, ${this.unvalidatedCount} unvalidated`);
        } else {
          console.error('Failed to load validation counts:', data.message);
          // Use sample data for development
          this.validatedCount = 125;
          this.unvalidatedCount = 37;
        }
      } catch (error) {
        console.error('Error loading validation counts:', error);
        // Use sample data for development
        this.validatedCount = 125;
        this.unvalidatedCount = 37;
      }
    },

    // Load Action Tracker Alerts data
    loadActionTrackerAlerts() {
      console.log(`Loading Action Tracker Alerts for PQE owner: ${this.pqeOwner}`);

      // Generate sample data for demonstration
      // In a real implementation, this would fetch from the action tracker API
      this.inProgressAlerts = [
        {
          id: 'ip-1',
          group: 'Pavo HLA',
          severity: 'High',
          xFactor: '2.1',
          status: 'In-Progress',
          actionTrackerId: 'at-002' // Link to action tracker item
        },
        {
          id: 'ip-2',
          group: 'Parthenon HLA',
          severity: 'Medium',
          xFactor: '1.8',
          status: 'In-Progress',
          actionTrackerId: 'at-003' // Link to action tracker item
        }
      ];

      this.monitoredAlerts = [
        {
          id: 'mon-1',
          group: 'Signal Integrity',
          severity: 'Medium',
          xFactor: '1.6',
          status: 'Monitored',
          actionTrackerId: 'at-001' // Link to action tracker item
        }
      ];

      this.resolvedAlerts = [
        {
          id: 'res-1',
          group: 'Manufacturing Defect',
          severity: 'High',
          xFactor: '0.8',
          status: 'Resolved',
          actionTrackerId: 'at-005' // Link to action tracker item
        },
        {
          id: 'res-2',
          group: 'Component Quality',
          severity: 'Medium',
          xFactor: '0.9',
          status: 'Resolved',
          actionTrackerId: 'at-006' // Link to action tracker item
        }
      ];

      console.log(`Loaded ${this.totalActionTrackerAlerts} Action Tracker Alerts`);
    },

    toggleCriticalIssuesExpanded(isOpen) {
      this.isCriticalIssuesExpanded = isOpen;
    },

    // Critical Issues Table Methods
    toggleIssueExpanded(issueId) {
      const index = this.expandedIssueIds.indexOf(issueId);
      if (index === -1) {
        // Issue is not expanded, so expand it
        this.expandedIssueIds.push(issueId);
      } else {
        // Issue is expanded, so collapse it
        this.expandedIssueIds.splice(index, 1);
      }
    },

    isIssueExpanded(issueId) {
      return this.expandedIssueIds.includes(issueId);
    },

    handleCriticalIssueRowClick(event) {
      // Handle row click for expandable functionality
      if (event && event.data) {
        this.toggleIssueExpanded(event.data.id);
      }
    },


    handleSeverityFilterChange() {
      // Update the selected filters based on the severity dropdown
      if (this.severityFilter === 'all') {
        this.selectedFilters.severity = [];
      } else {
        this.selectedFilters.severity = [this.severityFilter];
      }
    },

    handleAnalysisFilterChange() {
      // Update the selected filters based on the analysis type dropdown
      if (this.analysisTypeFilter === 'all') {
        this.selectedFilters.analysisType = [];
      } else {
        this.selectedFilters.analysisType = [this.analysisTypeFilter];
      }
    },

    clearFilters() {
      this.selectedFilters.severity = [];
      this.selectedFilters.analysisType = [];
      this.severityFilter = 'all';
      this.analysisTypeFilter = 'all';
    },

    handleResolvedCategoryFilterChange() {
      // Update the selected filters based on the category dropdown
      if (this.resolvedCategoryFilter === 'all') {
        this.resolvedFilters.category = [];
      } else {
        this.resolvedFilters.category = [this.resolvedCategoryFilter];
      }
    },

    handleResolvedAnalysisFilterChange() {
      // Update the selected filters based on the analysis type dropdown
      if (this.resolvedAnalysisTypeFilter === 'all') {
        this.resolvedFilters.analysisType = [];
      } else {
        this.resolvedFilters.analysisType = [this.resolvedAnalysisTypeFilter];
      }
    },

    clearResolvedFilters() {
      this.resolvedFilters.category = [];
      this.resolvedFilters.analysisType = [];
      this.resolvedCategoryFilter = 'all';
      this.resolvedAnalysisTypeFilter = 'all';
    },

    handleOutstandingCategoryFilterChange() {
      // Update the selected filters based on the category dropdown
      if (this.outstandingCategoryFilter === 'all') {
        this.outstandingFilters.category = [];
      } else {
        this.outstandingFilters.category = [this.outstandingCategoryFilter];
      }
    },

    handleOutstandingAnalysisFilterChange() {
      // Update the selected filters based on the analysis type dropdown
      if (this.outstandingAnalysisTypeFilter === 'all') {
        this.outstandingFilters.analysisType = [];
      } else {
        this.outstandingFilters.analysisType = [this.outstandingAnalysisTypeFilter];
      }
    },

    clearOutstandingFilters() {
      this.outstandingFilters.category = [];
      this.outstandingFilters.analysisType = [];
      this.outstandingCategoryFilter = 'all';
      this.outstandingAnalysisTypeFilter = 'all';
    },

    updateIssue(issue, markAsResolved = false, markAsOutstanding = false) {
      console.log('Update issue:', issue, 'Mark as resolved:', markAsResolved, 'Mark as outstanding:', markAsOutstanding);

      // Emit event to update action tracker
      this.$emit('update-action-tracker', {
        issueId: issue.id,
        category: issue.category,
        comment: issue.comment,
        severity: issue.severity,
        pqeOwner: this.pqeOwner,
        month: issue.month,
        analysisType: issue.analysisType,
        resolved: markAsResolved,
        outstanding: markAsOutstanding
      });

      // Handle status changes directly
      if (markAsResolved || markAsOutstanding) {
        // Remove from unresolved issues
        const index = this.unresolvedCriticalIssues.findIndex(i => i.id === issue.id);
        if (index !== -1) {
          this.unresolvedCriticalIssues.splice(index, 1);
        }

        // Show appropriate success message
        const statusType = markAsResolved ? 'resolved' : 'outstanding';
        alert(`Issue marked as ${statusType}: ${issue.category}`);
      } else {
        // Show success message for regular update
        alert(`Action tracker updated for issue: ${issue.category}`);
      }
    },

    viewPerformanceData(issue) {
      console.log('View performance data for:', issue.category);

      // In a real implementation, this would show a modal or chart with performance data
      // For now, we'll just show an alert with the data

      const performanceData = this.performanceData[issue.category];
      if (performanceData && performanceData.length > 0) {
        const performanceText = performanceData
          .map(data => `${data.month}: ${data.xFactor}x`)
          .join('\n');

        alert(`Performance data for ${issue.category}:\n${performanceText}`);
      } else {
        alert(`No performance data available for ${issue.category}`);
      }
    },

    viewIssueDetails(issue) {
      console.log('View issue details for:', issue.category);
      // In a real implementation, this would show a modal or navigate to a detailed view
      // Instead of alert, open the new critical issue modal
      this.openCriticalIssueModal(issue);
    },

    // Root Cause Chart methods
    async loadRootCauseData() {
      console.log('Loading root cause chart data for PQE Owner Dashboard');
      this.isRootCauseDataLoading = true;

      try {
        // Get authentication config
        const config = this.getAuthConfig();

        // Calculate date range based on selected time range
        const endDate = new Date();
        const startDate = new Date();
        const monthsToFetch = this.rootCauseTimeRange === '3month' ? 2 : 5; // 3 or 6 months including current
        startDate.setMonth(endDate.getMonth() - monthsToFetch);

        const startDateStr = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;
        const endDateStr = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;

        console.log(`Fetching root cause data for ${this.pqeOwner} from ${startDateStr} to ${endDateStr}, Group: ${this.rootCauseSelectedGroup}`);

        // Call the new API endpoint
        const response = await fetch('/api-statit2/get_pqe_root_cause_analysis', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...config.headers
          },
          body: JSON.stringify({
            pqeOwner: this.pqeOwner,
            startDate: startDateStr,
            endDate: endDateStr,
            selectedGroup: this.rootCauseSelectedGroup === 'all' ? null : this.rootCauseSelectedGroup,
            viewBy: this.rootCauseViewBy
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch root cause data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status_res === 'success') {
          // Transform the API data to chart format
          if (this.rootCauseViewBy === "rootCause") {
            this.rootCauseTitle = 'Root Cause Analysis';
          } else if (this.rootCauseViewBy === "vintage") {
            this.rootCauseTitle = 'Vintage Code Analysis';
          } else if (this.rootCauseViewBy === "sector") {
            this.rootCauseTitle = 'Sector Analysis';
          } else if (this.rootCauseViewBy === "supplier") {
            this.rootCauseTitle = 'Supplier Code Analysis';
          } else if (this.rootCauseViewBy === "partNum") {
            this.rootCauseTitle = 'Part Number Analysis';
          }
          this.rootCauseChartData = this.transformRootCauseData(data.categoryData);
          console.log('Root cause chart data loaded:', data.categoryData);
          console.log('Chart data length:', this.rootCauseChartData.length);
          console.log('Sample data point:', this.rootCauseChartData[0]);
          console.log('Breakout groups:', data.breakoutGroups);
          console.log('Part numbers:', data.partNumbers);

          console.log('Chart data loaded, watcher should handle update');
        } else {
          console.error('Failed to load root cause data:', data.sql_error_msg || data.message);
          // Fall back to mock data
          this.rootCauseChartData = this.generateMockRootCauseData();
          console.log('Using mock data:', this.rootCauseChartData.length);
        }
      } catch (error) {
        console.error('Error loading root cause data:', error);
        // Fall back to mock data
        this.rootCauseChartData = this.generateMockRootCauseData();
      } finally {
        this.isRootCauseDataLoading = false;
      }
    },

    transformRootCauseData(categoryData) {
      // Transform the API response into chart format
      const chartData = [];

      // categoryData structure: { "category": { "2024-03": { defects: 5, volume: 1000, failRate: 0.5 }, ... }, ... }
      for (const [category, monthData] of Object.entries(categoryData)) {
        // Clean up category name (trim whitespace)
        const cleanCategory = category.trim();
        console.log("trim cat", cleanCategory)

        for (const [month, data] of Object.entries(monthData)) {
          chartData.push({
            group: cleanCategory,
            key: month,
            value: parseFloat(data.failRate.toFixed(2))
          });
        }
      }

      console.log('Transformed root cause data:', chartData);
      console.log('Unique groups in data:', [...new Set(chartData.map(d => d.group))]);
      console.log('Unique keys in data:', [...new Set(chartData.map(d => d.key))]);
      console.log('Value range:', Math.min(...chartData.map(d => d.value)), 'to', Math.max(...chartData.map(d => d.value)));
      return chartData;
    },

    generateMockRootCauseData() {
      // Use the actual breakout groups for this PQE owner instead of generic categories
      let categories = [];

      if (this.breakoutGroups && this.breakoutGroups.length > 0) {
        // Use the actual breakout groups for this PQE owner
        categories = this.breakoutGroups.map(group => group.name);

        // If a specific group is selected, filter to only that group
        if (this.rootCauseSelectedGroup !== 'all') {
          categories = categories.filter(category => category === this.rootCauseSelectedGroup);
        }
      } else {
        // Fallback to sample data if breakout groups aren't loaded yet
        if (this.pqeOwner === 'Albert G.') {
          categories = ['Fan Themis', 'Victoria Crypto', 'Quantum Nexus'];
        } else if (this.pqeOwner === 'Sarah L.') {
          categories = ['Stellar Core', 'Nebula Drive'];
        } else {
          categories = ['Sample Group 1', 'Sample Group 2'];
        }

        // If a specific group is selected, filter to only that group
        if (this.rootCauseSelectedGroup !== 'all') {
          categories = categories.filter(category => category === this.rootCauseSelectedGroup);
        }
      }

      // Generate months based on selected time range
      const now = new Date();
      const months = [];
      const monthsToGenerate = this.rootCauseTimeRange === '3month' ? 3 : 6;

      for (let i = monthsToGenerate - 1; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        months.push(`${year}-${month}`);
      }

      const data = [];

      months.forEach(month => {
        categories.forEach(category => {
          // Generate realistic fail rate data (0-5% range)
          const baseRate = Math.random() * 3 + 0.5; // 0.5% to 3.5%
          const variation = (Math.random() - 0.5) * 1; // ±0.5%
          const failRate = Math.max(0.1, baseRate + variation);

          data.push({
            group: category,
            key: month,
            value: parseFloat(failRate.toFixed(2))
          });
        });
      });

      return data;
    },

    handleRootCauseBarClick(event) {
      if (event && event.data) {
        const clickedData = event.data;
        console.log(`Root cause bar clicked for category: ${clickedData.group}, month: ${clickedData.key}`);

        // You can implement additional functionality here, such as showing more details
        // or navigating to the MetisXFactors Group tab with this specific category
        alert(`Clicked on ${clickedData.group} for ${clickedData.key}\nFail Rate: ${clickedData.value}%`);
      }
    },

    handleRootCauseTimeRangeChange() {
      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);
      this.loadRootCauseData();
    },

    handleRootCauseViewByChange() {
      console.log(`Root cause time range changed to: ${this.rootCauseTimeRange}`);
      this.loadRootCauseData();
    },

    handleRootCauseGroupChange() {
      console.log(`Root cause group changed to: ${this.rootCauseSelectedGroup}`);
      console.log('Current chart data length before reload:', this.rootCauseChartData.length);
      this.loadRootCauseData();
    },

    getAuthConfig() {
      // Get authentication token from localStorage or Vuex store
      const token = localStorage.getItem('token') || this.$store.state.token;

      return {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      };
    },

    // Navigate to Action Tracker
    goToActionTracker() {
      console.log('Navigating to Action Tracker...');
      this.$router.push('/action-tracker');
    },

    // Navigate to Validation Page
    goToValidationPage() {
      console.log('Navigating to Validation Page...');
      this.$router.push('/defect-validations');
    },

    // Navigate to Heatmap tab in Metis XFactors
    goToHeatmap() {
      console.log('Navigating to Metis XFactors Heatmap...');
      this.$router.push('/metis-xfactors?tab=heatmap');
    },

    // Scroll to alerts section (action tracker alerts section)
    scrollToAlerts() {
      console.log('Scrolling to action tracker alerts section...');
      // Scroll to the Action Tracker Alerts section
      this.$nextTick(() => {
        const actionTrackerSection = document.querySelector('.section-card:first-child'); // First section card (Action Tracker Alerts)
        if (actionTrackerSection) {
          actionTrackerSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });

          // Add a brief highlight effect
          actionTrackerSection.style.transition = 'box-shadow 0.3s ease';
          actionTrackerSection.style.boxShadow = '0 0 20px rgba(0, 98, 255, 0.5)';

          setTimeout(() => {
            actionTrackerSection.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';
          }, 2000);
        }
      });
    },

    // Toggle Action Tracker Alerts section
    toggleActionTrackerAlertsExpanded(isOpen) {
      this.isActionTrackerAlertsExpanded = isOpen;
      console.log('Action Tracker Alerts expanded:', this.isActionTrackerAlertsExpanded);
    },

    // Toggle In-Progress Alerts subsection
    toggleInProgressAlertsExpanded() {
      this.isInProgressAlertsExpanded = !this.isInProgressAlertsExpanded;
      console.log('In-Progress Alerts expanded:', this.isInProgressAlertsExpanded);
    },

    // Toggle Monitored Alerts subsection
    toggleMonitoredAlertsExpanded() {
      this.isMonitoredAlertsExpanded = !this.isMonitoredAlertsExpanded;
      console.log('Monitored Alerts expanded:', this.isMonitoredAlertsExpanded);
    },

    // Toggle Resolved Alerts subsection
    toggleResolvedAlertsExpanded() {
      this.isResolvedAlertsExpanded = !this.isResolvedAlertsExpanded;
      console.log('Resolved Alerts expanded:', this.isResolvedAlertsExpanded);
    },

    // View Action Tracker Alert - opens tracking modal
    async viewActionTrackerAlert(alert) {
      console.log('Opening tracking modal for alert:', alert);

      // If the alert has an actionTrackerId, load the actual action tracker item
      if (alert.actionTrackerId) {
        try {
          // Load the actual action tracker item from the API or local data
          const actionTrackerItem = await this.loadActionTrackerItem(alert.actionTrackerId);

          if (actionTrackerItem) {
            this.selectedTrackingItem = actionTrackerItem;
          } else {
            // Fallback to creating a mock item if not found
            this.selectedTrackingItem = this.createMockActionTrackerItem(alert);
          }
        } catch (error) {
          console.error('Error loading action tracker item:', error);
          // Fallback to creating a mock item
          this.selectedTrackingItem = this.createMockActionTrackerItem(alert);
        }
      } else {
        // Create a mock action tracker item from the alert data
        this.selectedTrackingItem = this.createMockActionTrackerItem(alert);
      }

      // Load alert data
      await this.loadAlertUpdates(this.selectedTrackingItem);
      await this.loadAiInsight(this.selectedTrackingItem, alert);
      await this.loadAlertHistoryData(this.selectedTrackingItem);

      // Open the modal
      this.trackingModalVisible = true;
    },

    // Helper method to create a mock action tracker item from alert data
    createMockActionTrackerItem(alert) {
      return {
        id: alert.actionTrackerId || alert.id,
        pn: alert.pn || 'Unknown',
        group: alert.group || 'Unknown',
        status: alert.status || 'Unknown',
        priority: alert.severity || 'Medium',
        progress: alert.status === 'Resolved' ? 100 : (alert.status === 'In-Progress' ? 50 : 25),
        commodity: alert.commodity || 'Unknown',
        assignee: alert.assignee || 'PQE Team',
        action: `Address ${alert.group} issue with X-Factor of ${alert.xFactor}`,
        actionItems: [
          {
            title: `Investigate ${alert.group} root cause`,
            description: `Analyze the ${alert.severity} severity issue causing ${alert.xFactor}x performance`,
            completed: alert.status === 'Resolved',
            completedDate: alert.status === 'Resolved' ? new Date().toISOString().split('T')[0] : null,
            lastUpdated: new Date().toISOString().split('T')[0]
          }
        ]
      };
    },

    // Helper method to load actual action tracker item (placeholder for real implementation)
    async loadActionTrackerItem(actionTrackerId) {
      // In a real implementation, this would call an API to get the action tracker item
      // For now, return null to use the mock data
      console.log(`Loading action tracker item: ${actionTrackerId}`);

      // Mock data for demonstration - in real implementation, this would be an API call
      const mockActionTrackerItems = {
        'at-001': {
          id: 'at-001',
          pn: 'Signal Integrity',
          group: 'Signal Integrity',
          status: 'Monitored',
          priority: 'Medium',
          progress: 25,
          commodity: 'Processor',
          assignee: 'John Smith',
          action: 'Monitor signal integrity issues and implement corrective actions',
          actionItems: [
            {
              title: 'Review signal routing design',
              description: 'Analyze PCB layout for signal integrity issues',
              completed: false,
              lastUpdated: '2024-01-15'
            },
            {
              title: 'Test signal quality',
              description: 'Perform signal quality measurements',
              completed: false,
              lastUpdated: '2024-01-10'
            }
          ]
        },
        'at-002': {
          id: 'at-002',
          pn: 'Thermal Management',
          group: 'Thermal Management',
          status: 'In-Progress',
          priority: 'High',
          progress: 60,
          commodity: 'Cooling System',
          assignee: 'Sarah Johnson',
          action: 'Improve thermal management to reduce failure rates',
          actionItems: [
            {
              title: 'Redesign heat sink',
              description: 'Optimize heat sink design for better thermal dissipation',
              completed: true,
              completedDate: '2024-01-12',
              lastUpdated: '2024-01-12'
            },
            {
              title: 'Test thermal performance',
              description: 'Validate thermal improvements under stress conditions',
              completed: false,
              lastUpdated: '2024-01-14'
            }
          ]
        }
      };

      return mockActionTrackerItems[actionTrackerId] || null;
    },

    // Add critical issue to action tracker
    async addCriticalIssueToActionTracker(issue) {
      console.log('Adding critical issue to action tracker:', issue);

      try {
        const response = await fetch('/api-statit2/update_pqe_action', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...this.getAuthConfig().headers
          },
          body: JSON.stringify({
            issueId: issue.id,
            category: issue.category,
            comment: issue.comment || `Critical issue: ${issue.aiDescription}`,
            severity: issue.severity,
            pqeOwner: this.pqeOwner,
            month: issue.month,
            analysisType: issue.analysisType
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to add to action tracker: ${response.status}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          alert(`Critical issue "${issue.category}" has been added to the action tracker with ID: ${data.actionId}`);
        } else {
          throw new Error(data.message || 'Failed to add to action tracker');
        }
      } catch (error) {
        console.error('Error adding critical issue to action tracker:', error);
        alert('Failed to add critical issue to action tracker: ' + error.message);
      }
    },

    // Load alert updates for the selected item
    async loadAlertUpdates(item) {
      try {
        const response = await fetch('/api-statit2/get_pqe_alert_history', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...this.getAuthConfig().headers
          },
          body: JSON.stringify({
            actionTrackerId: item.id,
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch alert history: ${response.status}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          this.alertUpdates = data.alert_history || [];
        } else {
          console.error('Failed to load alert updates:', data.message);
          this.alertUpdates = [];
        }
      } catch (error) {
        console.error('Error loading alert updates:', error);
        this.alertUpdates = [];
      }
    },

    // Load AI insight for the selected item
    async loadAiInsight(item, alertData = null) {
      this.isLoadingAiInsight = true;
      try {
        const alertInfo = alertData || {
          category: item.group || 'Unknown',
          severity: item.priority || 'Medium',
          xFactor: '1.5',
          status: item.status || 'Unknown'
        };

        const response = await fetch('/api-statit2/get_pqe_alert_ai_insight', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...this.getAuthConfig().headers
          },
          body: JSON.stringify({
            actionTrackerId: item.id,
            alertData: alertInfo,
            pqeOwner: this.pqeOwner
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch AI insight: ${response.status}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          this.aiInsight = data.ai_insight || 'No insight available';
        } else {
          console.error('Failed to load AI insight:', data.message);
          this.aiInsight = 'Unable to generate insight at this time';
        }
      } catch (error) {
        console.error('Error loading AI insight:', error);
        this.aiInsight = 'Unable to generate insight at this time';
      } finally {
        this.isLoadingAiInsight = false;
      }
    },

    // Add alert update
    async addAlertUpdate() {
      if (!this.newAlertUpdate.trim()) {
        alert('Please enter an update');
        return;
      }

      try {
        const response = await fetch('/api-statit2/add_pqe_alert_update', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...this.getAuthConfig().headers
          },
          body: JSON.stringify({
            actionTrackerId: this.selectedTrackingItem.id,
            update: this.newAlertUpdate,
            updatedBy: this.pqeOwner,
            alertType: 'PQE Update'
          })
        });

        if (!response.ok) {
          throw new Error(`Failed to add alert update: ${response.status}`);
        }

        const data = await response.json();
        if (data.status_res === 'success') {
          // Add the new alert to the history
          this.alertUpdates.push(data.alert);
          this.newAlertUpdate = '';
          console.log('Alert update added successfully');
        } else {
          throw new Error(data.message || 'Failed to add alert update');
        }
      } catch (error) {
        console.error('Error adding alert update:', error);
        alert('Failed to add alert update: ' + error.message);
      }
    },

    // Load alert history data for performance table
    async loadAlertHistoryData() {
      try {
        // Mock data for now - this would come from your API
        this.alertHistoryData = [
          {
            month: 'Jan',
            year: '2024',
            status: 'Normal',
            actualRate: '0.5',
            targetRate: '1.0',
            xFactor: '0.5',
            volume: '1000',
            defects: '5',
            notes: 'Within target'
          },
          {
            month: 'Feb',
            year: '2024',
            status: 'Alert',
            actualRate: '2.1',
            targetRate: '1.0',
            xFactor: '2.1',
            volume: '1200',
            defects: '25',
            notes: 'Above target - investigating'
          }
        ];
      } catch (error) {
        console.error('Error loading alert history data:', error);
        this.alertHistoryData = [];
      }
    },

    // Get alert row class for styling
    getAlertRowClass(record) {
      if (record.status === 'Alert') return 'alert-row';
      if (record.status === 'Normal') return 'normal-row';
      return '';
    },

    // Format date helper
    formatDate(dateString) {
      if (!dateString) return '';
      return new Date(dateString).toLocaleDateString();
    },

    // Update action item completion
    updateActionItemCompletion(actionItem) {
      if (actionItem.completed) {
        actionItem.completedDate = new Date().toISOString();
      } else {
        actionItem.completedDate = null;
      }
      actionItem.lastUpdated = new Date().toISOString();
    },

    // Add default action items
    addDefaultActionItems() {
      if (!this.selectedTrackingItem.actionItems) {
        this.selectedTrackingItem.actionItems = [];
      }

      const defaultItems = [
        {
          title: 'Investigate root cause',
          description: 'Analyze data to identify potential root causes',
          completed: false,
          lastUpdated: new Date().toISOString()
        },
        {
          title: 'Implement corrective action',
          description: 'Execute plan to address identified issues',
          completed: false,
          lastUpdated: new Date().toISOString()
        },
        {
          title: 'Monitor results',
          description: 'Track performance to verify effectiveness',
          completed: false,
          lastUpdated: new Date().toISOString()
        }
      ];

      this.selectedTrackingItem.actionItems.push(...defaultItems);
    }
  }
};
</script>

<style scoped>
.pqe-owner-dashboard-container {
  color: #f4f4f4;
}

/* Carbon Design System Styles */
.section-accordion {
  margin-bottom: 1.5rem;
  background-color: #262626;
  border: 1px solid #393939;
  border-radius: 4px;
}

.accordion-title-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.accordion-title-content {
  display: flex;
  flex-direction: column;
}

.accordion-title {
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
  color: #f4f4f4;
}

.accordion-subtitle {
  font-size: 0.875rem;
  color: #c6c6c6;
  margin-top: 0.25rem;
}

.accordion-badge {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 24px;
  height: 24px;
  border-radius: 12px;
  background-color: #393939;
  color: #f4f4f4;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0 8px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.accordion-badge.flashing {
  animation: pulse 2s infinite;
  background-color: #fa4d56;
}

.critical-issues-badge.flashing {
  background-color: #fa4d56;
}

/* Metric Tiles */
.key-metrics-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.metric-tile {
  height: 100%;
  cursor: pointer;
  transition: all 0.2s ease;
}

.metric-tile:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border-color: #4589ff;
}

.metric-content {
  display: flex;
  align-items: flex-start;
}

.metric-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 1rem;
  flex-shrink: 0;
}

.metric-icon-svg {
  width: 24px;
  height: 24px;
}

.metric-icon.alerts {
  background-color: rgba(250, 77, 86, 0.1);
  color: #fa4d56;
}

.metric-icon.in-progress {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.metric-icon.validated {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.metric-icon.groups-over-target {
  background-color: rgba(15, 98, 254, 0.1);
  color: #0f62fe;
}

.metric-details {
  flex: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: #c6c6c6;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.75rem;
  font-weight: 600;
  color: #f4f4f4;
  margin-bottom: 0.25rem;
}

.metric-description {
  font-size: 0.75rem;
  color: #8d8d8d;
}

.section-content {
  padding: 1rem;
}

.content-wrapper {
  padding: 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 400;
}

.last-updated-text {
  font-size: 0.875rem;
  color: #8d8d8d;
}

.key-metrics-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metric-card {
  background-color: #262626;
  border-radius: 8px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  border: 1px solid #333333;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card.clickable {
  cursor: pointer;

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    background-color: #353535; /* Darker gray on hover */
  }

  &:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  }
}

.metric-icon {
  margin-right: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
}

.metric-icon.alerts {
  background-color: rgba(250, 77, 86, 0.1);
  color: #fa4d56;
}

.metric-icon.in-progress {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.metric-icon.validated {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.metric-icon.new-issues {
  background-color: rgba(15, 98, 254, 0.1);
  color: #0f62fe;
}

.metric-icon.critical-issues {
  background-color: rgba(250, 77, 86, 0.1);
  color: #fa4d56;
}

.metric-icon.unvalidated {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.metric-icon.groups-over-target {
  background-color: rgba(138, 63, 252, 0.1);
  color: #8a3ffc;
}

/* Action Tracker Alerts Styles */
.alert-subsection {
  margin-bottom: 1.5rem;
  border: 1px solid #393939;
  border-radius: 8px;
  background-color: #2a2a2a;
}

.subsection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  cursor: pointer;
  border-bottom: 1px solid #393939;
  transition: background-color 0.2s ease;
}

.subsection-header:hover {
  background-color: #333333;
}

.subsection-title {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 500;
  margin: 0;
}

.subsection-controls {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.subsection-content {
  padding: 1rem 1.25rem;
}

.action-tracker-alert {
  background-color: #333333;
  border: 1px solid #525252;
  border-radius: 6px;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
}

.action-tracker-alert:hover {
  background-color: #393939;
  border-color: #6f6f6f;
}

.action-tracker-alert .issue-header {
  padding: 1rem;
}

.action-tracker-alert .issue-tags {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.action-tracker-alert .issue-title {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.action-tracker-alert .issue-metadata {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.action-tracker-alert .issue-multiplier {
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.action-tracker-alert .issue-multiplier.medium-severity {
  background-color: rgba(255, 131, 43, 0.2);
  color: #ff832b;
}

.action-tracker-alert .issue-multiplier.good-performance {
  background-color: rgba(36, 161, 72, 0.2);
  color: #24a148;
}

.no-data-message {
  text-align: center;
  color: #8d8d8d;
  font-style: italic;
  padding: 2rem;
}

.metric-content {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-bottom: 0.25rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.metric-description {
  font-size: 0.75rem;
  color: #8d8d8d;
}

.chart-section {
  margin-bottom: 1.5rem;
}

.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  padding: 1rem;
  background-color: #262626;
  border-bottom: 1px solid #333333;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  color: #8d8d8d;
  font-size: 0.875rem;
  white-space: nowrap;
}

.chart-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
  background-color: #262626;
  padding: 1rem;
  border-radius: 4px;
  border: 1px solid #393939;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-label {
  font-size: 0.875rem;
  color: #c6c6c6;
  font-weight: 400;
}

.control-dropdown {
  width: 200px;
}

.chart-container {
  height: 500px;
  padding: 1rem;
  background-color: #161616;
  border-radius: 4px;
  margin: 1rem 0;
  border: 1px solid #393939;
}

.section-footer {
  padding: 0 1.25rem 1.25rem;
}

.section-description {
  margin: 0;
  font-size: 0.875rem;
  color: #8d8d8d;
  line-height: 1.4;
}

.dashboard-main-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.dashboard-column {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.section-card {
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.section-header:hover {
  background-color: #333333;
}

.section-title-container {
  display: flex;
  flex-direction: column;
}

.section-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 400;
}

.section-subtitle {
  font-size: 0.875rem;
  color: #8d8d8d;
  margin-top: 0.25rem;
}

.section-controls {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #fa4d56;
  color: #ffffff;
  font-size: 0.75rem;
  font-weight: 600;
  margin-right: 0.75rem;
}

.status-indicator.flashing {
  animation: flash 2s infinite;
}

.status-indicator.resolved-indicator {
  background-color: #24a148;
  min-width: 24px;
  min-height: 24px;
}


.status-indicator.action-tracker-indicator {
  background-color: #fa4d56;
  min-width: 24px;
  min-height: 24px;
}

.status-indicator.critical-issues-indicator {
  background-color: #da1e28;
  min-width: 24px;
  min-height: 24px;
}

.status-indicator.in-progress-indicator {
  background-color: #f18a0b;
  min-width: 24px;
  min-height: 24px;
}

.status-indicator.monitored-indicator {
  background-color: #0f62fe;
  min-width: 24px;
  min-height: 24px;
}


@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.expand-indicator {
  transition: transform 0.2s ease;
}

.expand-indicator.expanded {
  transform: rotate(180deg);
}

.section-content {
  padding: 0 1.25rem 1.25rem;
}

.filter-container {
  margin-bottom: 1.5rem;
  background-color: #333333;
  padding: 1rem;
  border-radius: 8px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.filter-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 400;
}

.filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-label {
  color: #8d8d8d;
  font-size: 0.875rem;
  white-space: nowrap;
}

.filter-dropdown {
  width: 200px;
}

.issues-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.issue-card {
  background-color: #333333;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  cursor: pointer;
}

.issue-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.issue-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  position: relative;
}

.issue-tags {
  display: flex;
  gap: 0.5rem;
  margin-right: 1rem;
}

.issue-title {
  flex-grow: 1;
  font-weight: 500;
}

.issue-metadata {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-right: 1rem;
}

.issue-multiplier {
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
}

.issue-multiplier.high-severity {
  background-color: rgba(250, 77, 86, 0.1);
  color: #fa4d56;
}

.issue-multiplier.medium-severity {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.issue-multiplier.good-performance {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.issue-multiplier.medium-performance {
  background-color: rgba(255, 131, 43, 0.1);
  color: #ff832b;
}

.resolved-issue-card {
  border-left: 4px solid #24a148;
}

.outstanding-issue-card {
  border-left: 4px solid #0f62fe;
}

.resolution-details, .acceptance-details {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: #c6c6c6;
}

.issue-multiplier.low-performance {
  background-color: rgba(36, 161, 72, 0.1);
  color: #24a148;
}

.resolution-comment, .acceptance-comment {
  margin: 1rem 0;
  padding: 0.75rem;
  background-color: #333333;
  border-radius: 4px;
}

.comment-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #c6c6c6;
}

.comment-text {
  font-size: 0.875rem;
  white-space: pre-wrap;
}

/* Critical Issues Table Styles */
.expanded-row {
  background-color: #262626;
}

.expanded-content {
  padding: 1.5rem;
  border-top: 1px solid #393939;
}

.expanded-section {
  margin-bottom: 1.5rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #f4f4f4;
  margin-bottom: 0.75rem;
}

.ai-description {
  padding: 1rem;
  background-color: #333333;
  border-radius: 4px;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #f4f4f4;
}

.action-comment {
  margin-bottom: 0.5rem;
}

.issue-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-group {
  display: flex;
  gap: 0.5rem;
  
  &.primary-actions {
    justify-content: flex-start;
  }
  
  &.status-actions {
    justify-content: flex-start;
  }
  
  &.tracker-actions {
    justify-content: flex-end;
  }
}

@media (min-width: 768px) {
  .issue-actions {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  .action-group {
    flex: 1;
    
    &.primary-actions {
      justify-content: flex-start;
    }
    
    &.status-actions {
      justify-content: center;
    }
    
    &.tracker-actions {
      justify-content: flex-end;
    }
  }
}

.no-data-message {
  color: #8d8d8d;
  font-size: 1rem;
  text-align: center;
  padding: 2rem;
}

@media (max-width: 768px) {
  .key-metrics-section {
    grid-template-columns: 1fr;
  }

  .dashboard-main-content {
    grid-template-columns: 1fr;
  }

  .filter-controls {
    flex-direction: column;
  }

  .filter-group {
    width: 100%;
  }
}

/* Issue actions styling */
.issue-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #444444;
}

.issue-actions .cv-button {
  flex: 0 0 auto;
}

@media (max-width: 768px) {
  .issue-actions {
    flex-direction: column;
  }

  .issue-actions .cv-button {
    width: 100%;
  }
}

/* Action Tracker Modal Styles */
.tracking-modal .cv-modal-container {
  max-width: 95vw;
  width: 1200px;
}

.tracking-modal-content {
  padding: 1rem;
  color: #f4f4f4;
  background-color: #262626;
  border-radius: 4px;
}

.tracking-tab-content {
  padding: 1.5rem;
  background-color: #262626;
  border-radius: 4px;
  margin-top: 1rem;
  border: 1px solid #393939;
}

.section-title {
  color: #f4f4f4;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  border-bottom: 1px solid #444444;
  padding-bottom: 0.5rem;
}

.section-description {
  color: #c6c6c6;
  font-size: 0.875rem;
  margin-bottom: 2rem;
}

.subsection-title {
  color: #f4f4f4;
  font-size: 1rem;
  font-weight: 500;
  margin: 0 0 1rem 0;
  border-bottom: 1px solid #444444;
  padding-bottom: 0.5rem;
}

/* New Alert Functionality Styles */
.new-alerts-section {
  padding: 0;
}

.ai-insight-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #444444;
}

.ai-insight-content {
  background-color: #262626;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #525252;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #f4f4f4;
}

.loading-message {
  text-align: center;
  color: #8d8d8d;
  font-style: italic;
  padding: 1rem;
}

.add-alert-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #444444;
}

.add-update-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.update-form-actions {
  display: flex;
  justify-content: flex-end;
}

.alert-updates-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #444444;
}

.alert-updates-table {
  background-color: #262626;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #525252;
}

.updates-header {
  display: grid;
  grid-template-columns: 120px 1fr 150px;
  background-color: #393939;
  border-bottom: 1px solid #525252;
}

.update-column {
  padding: 0.75rem;
  font-weight: 600;
  color: #f4f4f4;
  border-right: 1px solid #525252;
}

.update-column:last-child {
  border-right: none;
}

.update-row {
  display: grid;
  grid-template-columns: 120px 1fr 150px;
  border-bottom: 1px solid #525252;
}

.update-row:last-child {
  border-bottom: none;
}

.update-cell {
  padding: 0.75rem;
  color: #f4f4f4;
  border-right: 1px solid #525252;
  font-size: 0.875rem;
}

.update-cell:last-child {
  border-right: none;
}

.no-updates-message {
  text-align: center;
  color: #8d8d8d;
  font-style: italic;
  padding: 2rem;
}

/* Action Items Tab Styles */
.tracking-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #444444;
}

.tracking-section-title {
  color: #f4f4f4;
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0 0 1rem 0;
  border-bottom: 1px solid #393939;
  padding-bottom: 0.5rem;
}

.action-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.summary-label {
  font-weight: 600;
  color: #c6c6c6;
  font-size: 0.875rem;
}

.summary-value {
  color: #f4f4f4;
  font-size: 1rem;
}

.priority-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  width: fit-content;
}

.priority-badge.high {
  background-color: #da1e28;
  color: #ffffff;
}

.priority-badge.medium {
  background-color: #f1c21b;
  color: #000000;
}

.priority-badge.low {
  background-color: #24a148;
  color: #ffffff;
}

.action-items-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.action-item-card {
  background-color: #262626;
  border-radius: 6px;
  padding: 1rem;
  border: 1px solid #525252;
}

.action-item-card.completed {
  opacity: 0.7;
  background-color: #1e3a1e;
}

.action-item-header {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.completion-date,
.last-updated {
  font-size: 0.75rem;
  color: #8d8d8d;
}

.action-item-description {
  margin-top: 0.5rem;
  color: #c6c6c6;
  font-size: 0.875rem;
}

.no-action-items {
  text-align: center;
  padding: 2rem;
  color: #8d8d8d;
}

/* Performance Chart & History Tab Styles */
.chart-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid #444444;
}

.performance-history-section {
  background-color: #333333;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #444444;
}

.alert-history-table {
  margin-top: 1rem;
}

.status-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-indicator.normal {
  background-color: #24a148;
}

.status-indicator.alert {
  background-color: #da1e28;
}

.status-text.normal {
  color: #24a148;
}

.status-text.alert {
  color: #da1e28;
}

.alert-row {
  background-color: rgba(218, 30, 40, 0.1);
}

.normal-row {
  background-color: rgba(36, 161, 72, 0.1);
}

.no-alert-data {
  text-align: center;
  color: #8d8d8d;
  padding: 2rem;
}

.no-alert-data .note {
  font-size: 0.875rem;
  font-style: italic;
}

@media (max-width: 768px) {
  .tracking-modal .cv-modal-container {
    max-width: 95vw;
    width: 95vw;
  }

  .updates-header,
  .update-row {
    grid-template-columns: 1fr;
  }

  .update-column,
  .update-cell {
    border-right: none;
    border-bottom: 1px solid #525252;
  }

  .action-summary {
    grid-template-columns: 1fr;
  }
}
</style>

const configSwitch = require("./config.js");
const ibmdb = require("ibm_db");
const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');
const logger = require('../utils/logger');
const axios = require('axios');

// Export controller functions
exports.get_phase1_part_groups = (req, res) => {
  getPhase1PartGroups(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_phase1_part_groups:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.execute_phase1_dropdown_query = (req, res) => {
  executePhase1DropdownQuery(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in execute_phase1_dropdown_query:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.execute_phase1_ai_query = (req, res) => {
  executePhase1AiQuery(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in execute_phase1_ai_query:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// Helper function to get database connection
function getDbConnection() {
  const config = configSwitch.config();
  return config.db_connection_string;
}

// Get part groups for Phase 1 analysis
async function getPhase1PartGroups(values, callback) {
  let responseObj = {
    status_res: "success",
    part_groups: []
  };

  try {
    const process = values.process || 'all';
    
    logger.logInfo(`Getting part groups for process: ${process}`, 'getPhase1PartGroups');

    // Sample part groups - in a real implementation, this would query the database
    const allPartGroups = [
      { value: 'cpu', label: 'CPU Components' },
      { value: 'memory', label: 'Memory Modules' },
      { value: 'storage', label: 'Storage Devices' },
      { value: 'network', label: 'Network Components' },
      { value: 'power', label: 'Power Management' },
      { value: 'cooling', label: 'Cooling Systems' },
      { value: 'sensors', label: 'Sensor Arrays' },
      { value: 'connectors', label: 'Connectors & Cables' }
    ];

    // Filter based on process if needed
    if (process === 'FAB') {
      responseObj.part_groups = allPartGroups.filter(group => 
        ['cpu', 'memory', 'sensors', 'connectors'].includes(group.value)
      );
    } else if (process === 'FUL') {
      responseObj.part_groups = allPartGroups.filter(group => 
        ['storage', 'network', 'power', 'cooling'].includes(group.value)
      );
    } else {
      responseObj.part_groups = allPartGroups;
    }

    logger.logInfo(`Returning ${responseObj.part_groups.length} part groups`, 'getPhase1PartGroups');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in getPhase1PartGroups:', error);
    callback(error, null);
  }
}

// Execute dropdown-based query
async function executePhase1DropdownQuery(values, callback) {
  let responseObj = {
    status_res: "success",
    chart_data: [],
    chart_type: "bar",
    title: ""
  };

  try {
    const { viewBy, timeRange, customDateRange, process, partGroup } = values;
    
    logger.logInfo(`Executing dropdown query - ViewBy: ${viewBy}, TimeRange: ${timeRange}, Process: ${process}, PartGroup: ${partGroup}`, 'executePhase1DropdownQuery');

    // Generate sample data based on parameters
    responseObj.chart_data = generateSampleChartData(viewBy, timeRange, process, partGroup);
    responseObj.chart_type = (viewBy === 'vintage' || timeRange.includes('month')) ? 'line' : 'bar';
    responseObj.title = `${viewBy.charAt(0).toUpperCase() + viewBy.slice(1)} Analysis - ${timeRange.toUpperCase()}`;

    if (process !== 'all') {
      responseObj.title += ` (${process})`;
    }
    if (partGroup !== 'all') {
      responseObj.title += ` - ${partGroup}`;
    }

    logger.logInfo(`Generated ${responseObj.chart_data.length} data points`, 'executePhase1DropdownQuery');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in executePhase1DropdownQuery:', error);
    callback(error, null);
  }
}

// Execute AI-based query
async function executePhase1AiQuery(values, callback) {
  let responseObj = {
    status_res: "success",
    chart_data: [],
    chart_type: "bar",
    title: "AI Analysis Results",
    ai_response: ""
  };

  try {
    const { prompt } = values;
    
    logger.logInfo(`Executing AI query with prompt: ${prompt.substring(0, 100)}...`, 'executePhase1AiQuery');

    // First, analyze the prompt to determine what data to generate
    const analysisPrompt = `
Analyze this manufacturing data query and provide a structured response:

User Query: "${prompt}"

Please provide:
1. What type of analysis is being requested (root cause, trend, comparison, etc.)
2. What time period should be analyzed
3. What process or part group is the focus
4. What chart type would be most appropriate (bar, line)
5. Generate 5-8 realistic data points for this analysis

Respond in JSON format:
{
  "analysis_type": "string",
  "time_period": "string", 
  "focus_area": "string",
  "chart_type": "bar|line",
  "data_points": [{"label": "string", "value": number, "details": "string"}],
  "summary": "Brief analysis summary"
}
`;

    // Call WatsonX.ai API
    const watsonxResponse = await callWatsonXApi(analysisPrompt);
    
    if (watsonxResponse && watsonxResponse.status === 'success') {
      try {
        // Parse the AI response
        const aiAnalysis = JSON.parse(watsonxResponse.generated_text);
        
        // Convert AI analysis to chart data format
        responseObj.chart_data = aiAnalysis.data_points.map(point => ({
          group: point.label,
          value: point.value,
          details: point.details || ''
        }));
        
        responseObj.chart_type = aiAnalysis.chart_type || 'bar';
        responseObj.title = `AI Analysis: ${aiAnalysis.analysis_type}`;
        responseObj.ai_response = aiAnalysis.summary;
        
      } catch (parseError) {
        logger.logError('Error parsing AI response:', parseError);
        // Fallback to generating sample data
        responseObj.chart_data = generateSampleChartData('rootCause', '3month', 'all', 'all');
        responseObj.ai_response = watsonxResponse.generated_text || 'Analysis completed based on your query.';
      }
    } else {
      // Fallback if AI call fails
      logger.logError('WatsonX AI call failed, using fallback data');
      responseObj.chart_data = generateSampleChartData('rootCause', '3month', 'all', 'all');
      responseObj.ai_response = 'Analysis completed. The AI service is currently unavailable, but here are the results based on your query parameters.';
    }

    logger.logInfo(`AI query completed with ${responseObj.chart_data.length} data points`, 'executePhase1AiQuery');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in executePhase1AiQuery:', error);
    callback(error, null);
  }
}

// Helper function to call WatsonX.ai API
async function callWatsonXApi(prompt) {
  try {
    const requestData = {
      model_id: 'ibm/granite-13b-instruct-v2',
      prompt: prompt,
      temperature: 0.3,
      api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
      project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'
    };

    logger.logInfo('Calling WatsonX.ai API for Phase 1 analysis', 'callWatsonXApi');

    const response = await axios.post('http://localhost:8080/api-statit2/watsonx_prompt', requestData);
    
    if (response.data && response.data.status === 'success') {
      logger.logInfo('WatsonX.ai API call successful', 'callWatsonXApi');
      return response.data;
    } else {
      logger.logError('WatsonX.ai API returned error:', response.data);
      return null;
    }
  } catch (error) {
    logger.logError('Error calling WatsonX.ai API:', error);
    return null;
  }
}

// Helper function to generate sample chart data
function generateSampleChartData(viewBy, timeRange, process, partGroup) {
  const data = [];
  
  // Generate different data based on viewBy parameter
  switch (viewBy) {
    case 'rootCause':
      const rootCauses = ['Mechanical Failure', 'Electrical Issue', 'Software Bug', 'Material Defect', 'Process Variation'];
      rootCauses.forEach(cause => {
        data.push({
          group: cause,
          value: Math.floor(Math.random() * 50) + 10,
          details: `${cause} analysis for ${timeRange}`
        });
      });
      break;
      
    case 'vintage':
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
      months.forEach(month => {
        data.push({
          group: month,
          value: Math.floor(Math.random() * 30) + 5,
          details: `${month} vintage analysis`
        });
      });
      break;
      
    case 'sector':
      const sectors = ['Sector A', 'Sector B', 'Sector C', 'Sector D'];
      sectors.forEach(sector => {
        data.push({
          group: sector,
          value: Math.floor(Math.random() * 40) + 15,
          details: `${sector} performance`
        });
      });
      break;
      
    case 'supplier':
      const suppliers = ['Supplier 1', 'Supplier 2', 'Supplier 3', 'Supplier 4', 'Supplier 5'];
      suppliers.forEach(supplier => {
        data.push({
          group: supplier,
          value: Math.floor(Math.random() * 35) + 8,
          details: `${supplier} quality metrics`
        });
      });
      break;
      
    case 'partNum':
      const partNumbers = ['PN-001', 'PN-002', 'PN-003', 'PN-004', 'PN-005'];
      partNumbers.forEach(pn => {
        data.push({
          group: pn,
          value: Math.floor(Math.random() * 25) + 12,
          details: `${pn} failure analysis`
        });
      });
      break;
      
    case 'category':
      const categories = ['Hardware', 'Software', 'Firmware', 'Mechanical', 'Electrical'];
      categories.forEach(category => {
        data.push({
          group: category,
          value: Math.floor(Math.random() * 45) + 20,
          details: `${category} defect analysis`
        });
      });
      break;
      
    default:
      // Default data
      for (let i = 1; i <= 5; i++) {
        data.push({
          group: `Item ${i}`,
          value: Math.floor(Math.random() * 50) + 10,
          details: `Analysis item ${i}`
        });
      }
  }
  
  return data;
}

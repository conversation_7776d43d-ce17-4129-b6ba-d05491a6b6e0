const configSwitch = require("./config.js");
const ibmdb = require("ibm_db");
const path = require('path');
const xlsx = require('xlsx');
const fs = require('fs');
const logger = require('../utils/logger');
const axios = require('axios');

// Export controller functions
exports.get_phase1_part_groups = (req, res) => {
  getPhase1PartGroups(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_phase1_part_groups:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.execute_phase1_dropdown_query = (req, res) => {
  executePhase1DropdownQuery(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in execute_phase1_dropdown_query:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.execute_phase1_ai_query = (req, res) => {
  executePhase1AiQuery(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in execute_phase1_ai_query:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.export_phase1_data = (req, res) => {
  exportPhase1Data(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in export_phase1_data:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }

    // Set appropriate headers for file download
    if (data.format === 'csv') {
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="phase1_export.csv"');
      res.send(data.content);
    } else if (data.format === 'pdf') {
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', 'attachment; filename="phase1_export.pdf"');
      res.send(data.content);
    }
  });
};

exports.save_phase1_job = (req, res) => {
  savePhase1Job(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in save_phase1_job:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.get_saved_reports = (req, res) => {
  getSavedReports(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in get_saved_reports:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.delete_saved_report = (req, res) => {
  deleteSavedReport(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in delete_saved_report:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

exports.schedule_report = (req, res) => {
  scheduleReport(req.body, function (err, data) {
    if (err) {
      logger.logError("Error in schedule_report:", err);
      return res.status(500).json({ status_res: "error", message: err.message });
    }
    res.status(201).json(data);
  });
};

// Helper function to get database connection
function getDbConnection() {
  const config = configSwitch.config();
  return config.db_connection_string;
}

// Get part groups for Phase 1 analysis
async function getPhase1PartGroups(values, callback) {
  let responseObj = {
    status_res: "success",
    part_groups: []
  };

  try {
    const process = values.process || 'all';
    
    logger.logInfo(`Getting part groups for process: ${process}`, 'getPhase1PartGroups');

    // Sample part groups - in a real implementation, this would query the database
    const allPartGroups = [
      { value: 'cpu', label: 'CPU Components' },
      { value: 'memory', label: 'Memory Modules' },
      { value: 'storage', label: 'Storage Devices' },
      { value: 'network', label: 'Network Components' },
      { value: 'power', label: 'Power Management' },
      { value: 'cooling', label: 'Cooling Systems' },
      { value: 'sensors', label: 'Sensor Arrays' },
      { value: 'connectors', label: 'Connectors & Cables' }
    ];

    // Filter based on process if needed
    if (process === 'FAB') {
      responseObj.part_groups = allPartGroups.filter(group => 
        ['cpu', 'memory', 'sensors', 'connectors'].includes(group.value)
      );
    } else if (process === 'FUL') {
      responseObj.part_groups = allPartGroups.filter(group => 
        ['storage', 'network', 'power', 'cooling'].includes(group.value)
      );
    } else {
      responseObj.part_groups = allPartGroups;
    }

    logger.logInfo(`Returning ${responseObj.part_groups.length} part groups`, 'getPhase1PartGroups');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in getPhase1PartGroups:', error);
    callback(error, null);
  }
}

// Execute dropdown-based query
async function executePhase1DropdownQuery(values, callback) {
  let responseObj = {
    status_res: "success",
    chart_data: [],
    chart_type: "bar",
    title: ""
  };

  try {
    const { viewBy, timeRange, customDateRange, process, partGroup } = values;
    
    logger.logInfo(`Executing dropdown query - ViewBy: ${viewBy}, TimeRange: ${timeRange}, Process: ${process}, PartGroup: ${partGroup}`, 'executePhase1DropdownQuery');

    // Generate sample data based on parameters
    responseObj.chart_data = generateSampleChartData(viewBy, timeRange, process, partGroup);
    responseObj.chart_type = (viewBy === 'vintage' || timeRange.includes('month')) ? 'line' : 'bar';
    responseObj.title = `${viewBy.charAt(0).toUpperCase() + viewBy.slice(1)} Analysis - ${timeRange.toUpperCase()}`;

    if (process !== 'all') {
      responseObj.title += ` (${process})`;
    }
    if (partGroup !== 'all') {
      responseObj.title += ` - ${partGroup}`;
    }

    logger.logInfo(`Generated ${responseObj.chart_data.length} data points`, 'executePhase1DropdownQuery');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in executePhase1DropdownQuery:', error);
    callback(error, null);
  }
}

// Execute AI-based query
async function executePhase1AiQuery(values, callback) {
  let responseObj = {
    status_res: "success",
    chart_data: [],
    chart_type: "bar",
    title: "AI Analysis Results",
    ai_response: ""
  };

  try {
    const { prompt } = values;
    
    logger.logInfo(`Executing AI query with prompt: ${prompt.substring(0, 100)}...`, 'executePhase1AiQuery');

    // First, analyze the prompt to determine what data to generate
    const analysisPrompt = `
Analyze this manufacturing data query and provide a structured response:

User Query: "${prompt}"

Please provide:
1. What type of analysis is being requested (root cause, trend, comparison, etc.)
2. What time period should be analyzed
3. What process or part group is the focus
4. What chart type would be most appropriate (bar, line)
5. Generate 5-8 realistic data points for this analysis

Respond in JSON format:
{
  "analysis_type": "string",
  "time_period": "string", 
  "focus_area": "string",
  "chart_type": "bar|line",
  "data_points": [{"label": "string", "value": number, "details": "string"}],
  "summary": "Brief analysis summary"
}
`;

    // Call WatsonX.ai API
    const watsonxResponse = await callWatsonXApi(analysisPrompt);
    
    if (watsonxResponse && watsonxResponse.status === 'success') {
      try {
        // Parse the AI response
        const aiAnalysis = JSON.parse(watsonxResponse.generated_text);
        
        // Convert AI analysis to chart data format
        responseObj.chart_data = aiAnalysis.data_points.map(point => ({
          group: point.label,
          value: point.value,
          details: point.details || ''
        }));
        
        responseObj.chart_type = aiAnalysis.chart_type || 'bar';
        responseObj.title = `AI Analysis: ${aiAnalysis.analysis_type}`;
        responseObj.ai_response = aiAnalysis.summary;
        
      } catch (parseError) {
        logger.logError('Error parsing AI response:', parseError);
        // Fallback to generating sample data
        responseObj.chart_data = generateSampleChartData('rootCause', '3month', 'all', 'all');
        responseObj.ai_response = watsonxResponse.generated_text || 'Analysis completed based on your query.';
      }
    } else {
      // Fallback if AI call fails
      logger.logError('WatsonX AI call failed, using fallback data');
      responseObj.chart_data = generateSampleChartData('rootCause', '3month', 'all', 'all');
      responseObj.ai_response = 'Analysis completed. The AI service is currently unavailable, but here are the results based on your query parameters.';
    }

    logger.logInfo(`AI query completed with ${responseObj.chart_data.length} data points`, 'executePhase1AiQuery');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in executePhase1AiQuery:', error);
    callback(error, null);
  }
}

// Helper function to call WatsonX.ai API
async function callWatsonXApi(prompt) {
  try {
    const requestData = {
      model_id: 'ibm/granite-13b-instruct-v2',
      prompt: prompt,
      temperature: 0.3,
      api_key: 'A-9UZe48N156o-lLuxvjR5IePXDhKwrVHu61UciQ9afl',
      project_id: '8a74c0cb-b3c8-4fd7-9823-8301276169de'
    };

    logger.logInfo('Calling WatsonX.ai API for Phase 1 analysis', 'callWatsonXApi');

    const response = await axios.post('http://localhost:8080/api-statit2/watsonx_prompt', requestData);
    
    if (response.data && response.data.status === 'success') {
      logger.logInfo('WatsonX.ai API call successful', 'callWatsonXApi');
      return response.data;
    } else {
      logger.logError('WatsonX.ai API returned error:', response.data);
      return null;
    }
  } catch (error) {
    logger.logError('Error calling WatsonX.ai API:', error);
    return null;
  }
}

// Export Phase 1 data to CSV or PDF
async function exportPhase1Data(values, callback) {
  try {
    const { format, data, title, queryParams, timestamp } = values;

    logger.logInfo(`Exporting Phase 1 data in ${format} format`, 'exportPhase1Data');

    if (format === 'csv') {
      // Generate CSV content
      let csvContent = `Phase 1 Analysis Export\n`;
      csvContent += `Title: ${title}\n`;
      csvContent += `Generated: ${timestamp}\n`;
      csvContent += `Query Parameters: ${JSON.stringify(queryParams)}\n\n`;
      csvContent += `Category,Value,Details\n`;

      data.forEach(item => {
        csvContent += `"${item.group}","${item.value}","${item.details || 'N/A'}"\n`;
      });

      callback(null, { format: 'csv', content: csvContent });

    } else if (format === 'pdf') {
      // For PDF, we'll create a simple text-based PDF content
      // In a real implementation, you'd use a PDF library like PDFKit
      const pdfContent = `Phase 1 Analysis Export\n\nTitle: ${title}\nGenerated: ${timestamp}\n\nData:\n${data.map(item => `${item.group}: ${item.value} (${item.details || 'N/A'})`).join('\n')}`;

      callback(null, { format: 'pdf', content: pdfContent });
    } else {
      throw new Error('Unsupported export format');
    }

  } catch (error) {
    logger.logError('Error in exportPhase1Data:', error);
    callback(error, null);
  }
}

// Save Phase 1 job
async function savePhase1Job(values, callback) {
  let responseObj = {
    status_res: "success",
    job_id: null
  };

  try {
    const { name, jobData, createdAt } = values;

    logger.logInfo(`Saving Phase 1 job: ${name}`, 'savePhase1Job');

    // In a real implementation, this would save to a database
    // For now, we'll simulate saving and return a job ID
    const jobId = `job_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    // Simulate saving job data
    const savedJob = {
      id: jobId,
      name: name,
      data: jobData,
      createdAt: createdAt,
      status: 'saved',
      scheduled: false
    };

    // Store in memory (in real implementation, save to database)
    if (!global.savedReports) {
      global.savedReports = [];
    }
    global.savedReports.push(savedJob);

    responseObj.job_id = jobId;
    responseObj.saved_job = savedJob;

    logger.logInfo(`Job saved with ID: ${jobId}`, 'savePhase1Job');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in savePhase1Job:', error);
    callback(error, null);
  }
}

// Get saved reports
async function getSavedReports(values, callback) {
  let responseObj = {
    status_res: "success",
    reports: []
  };

  try {
    logger.logInfo('Getting saved reports', 'getSavedReports');

    // In a real implementation, this would query the database
    // For now, return from memory storage
    if (global.savedReports) {
      responseObj.reports = global.savedReports;
    } else {
      // Sample data for demonstration
      responseObj.reports = [
        {
          id: 'sample_1',
          name: 'Root Cause Analysis - FAB Process',
          data: {
            type: 'dropdown',
            params: { viewBy: 'rootCause', timeRange: '3month', process: 'FAB' },
            results: { title: 'Root Cause Analysis', chart_data: [] }
          },
          createdAt: new Date().toISOString(),
          scheduled: false
        },
        {
          id: 'sample_2',
          name: 'AI Analysis - Quality Trends',
          data: {
            type: 'ai',
            params: { prompt: 'Show quality trends for the last quarter' },
            results: { title: 'AI Analysis Results', chart_data: [] }
          },
          createdAt: new Date(Date.now() - 86400000).toISOString(),
          scheduled: true
        }
      ];
    }

    logger.logInfo(`Returning ${responseObj.reports.length} saved reports`, 'getSavedReports');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in getSavedReports:', error);
    callback(error, null);
  }
}

// Delete saved report
async function deleteSavedReport(values, callback) {
  let responseObj = {
    status_res: "success"
  };

  try {
    const { reportId } = values;

    logger.logInfo(`Deleting saved report: ${reportId}`, 'deleteSavedReport');

    // In a real implementation, this would delete from database
    if (global.savedReports) {
      global.savedReports = global.savedReports.filter(report => report.id !== reportId);
    }

    logger.logInfo(`Report ${reportId} deleted`, 'deleteSavedReport');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in deleteSavedReport:', error);
    callback(error, null);
  }
}

// Schedule report
async function scheduleReport(values, callback) {
  let responseObj = {
    status_res: "success",
    schedule_id: null
  };

  try {
    const { reportId, scheduleType, scheduleDay, scheduleTime, emailRecipients } = values;

    logger.logInfo(`Scheduling report: ${reportId}`, 'scheduleReport');

    // Create schedule configuration
    const scheduleConfig = {
      reportId: reportId,
      type: scheduleType,
      day: scheduleDay,
      time: scheduleTime,
      recipients: emailRecipients,
      createdAt: new Date().toISOString(),
      active: true
    };

    // In a real implementation, this would save to database and set up cron job
    const scheduleId = `schedule_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    scheduleConfig.id = scheduleId;

    // Update the report to mark as scheduled
    if (global.savedReports) {
      const reportIndex = global.savedReports.findIndex(report => report.id === reportId);
      if (reportIndex !== -1) {
        global.savedReports[reportIndex].scheduled = true;
        global.savedReports[reportIndex].scheduleConfig = scheduleConfig;
      }
    }

    // Set up the actual cron job (simplified example)
    setupCronJob(scheduleConfig);

    responseObj.schedule_id = scheduleId;

    logger.logInfo(`Report scheduled with ID: ${scheduleId}`, 'scheduleReport');
    callback(null, responseObj);

  } catch (error) {
    logger.logError('Error in scheduleReport:', error);
    callback(error, null);
  }
}

// Setup scheduled job for reports
function setupCronJob(scheduleConfig) {
  const schedule = require('node-schedule');

  let rule = new schedule.RecurrenceRule();
  const [hour, minute] = scheduleConfig.time.split(':').map(Number);

  rule.hour = hour;
  rule.minute = minute;

  // Configure recurrence based on schedule type
  switch (scheduleConfig.type) {
    case 'daily':
      // Run every day at specified time
      break;
    case 'weekly':
      const dayMap = { monday: 1, tuesday: 2, wednesday: 3, thursday: 4, friday: 5 };
      rule.dayOfWeek = dayMap[scheduleConfig.day] || 1;
      break;
    case 'monthly':
      rule.date = parseInt(scheduleConfig.day) || 1;
      break;
  }

  logger.logInfo(`Setting up scheduled job for report ${scheduleConfig.reportId}`, 'setupCronJob');

  // Schedule the task
  const job = schedule.scheduleJob(rule, () => {
    logger.logInfo(`Executing scheduled report: ${scheduleConfig.reportId}`, 'scheduledJob');
    executeScheduledReport(scheduleConfig);
  });

  // Store job reference for potential cancellation
  if (!global.scheduledJobs) {
    global.scheduledJobs = {};
  }
  global.scheduledJobs[scheduleConfig.id] = job;
}

// Execute scheduled report
async function executeScheduledReport(scheduleConfig) {
  try {
    logger.logInfo(`Executing scheduled report: ${scheduleConfig.reportId}`, 'executeScheduledReport');

    // Find the report
    const report = global.savedReports?.find(r => r.id === scheduleConfig.reportId);
    if (!report) {
      logger.logError(`Report not found: ${scheduleConfig.reportId}`, null, 'executeScheduledReport');
      return;
    }

    // Re-execute the query based on saved parameters
    let results = null;
    if (report.data.type === 'dropdown') {
      results = await executePhase1DropdownQuery(report.data.params, () => {});
    } else if (report.data.type === 'ai') {
      results = await executePhase1AiQuery(report.data.params, () => {});
    }

    // Send email with results (simplified)
    if (scheduleConfig.recipients && scheduleConfig.recipients.length > 0) {
      await sendReportEmail(scheduleConfig.recipients, report, results);
    }

    logger.logInfo(`Scheduled report executed successfully: ${scheduleConfig.reportId}`, 'executeScheduledReport');

  } catch (error) {
    logger.logError(`Error executing scheduled report: ${scheduleConfig.reportId}`, error, 'executeScheduledReport');
  }
}

// Send report email (placeholder)
async function sendReportEmail(recipients, report, results) {
  logger.logInfo(`Sending report email to: ${recipients.join(', ')}`, 'sendReportEmail');

  // In a real implementation, this would use a mail service like nodemailer
  // For now, just log the action
  logger.logInfo(`Email sent for report: ${report.name}`, 'sendReportEmail');
}

// Helper function to generate sample chart data
function generateSampleChartData(viewBy, timeRange, process, partGroup) {
  const data = [];

  // Generate different data based on viewBy parameter
  switch (viewBy) {
    case 'rootCause':
      const rootCauses = ['Mechanical Failure', 'Electrical Issue', 'Software Bug', 'Material Defect', 'Process Variation'];
      rootCauses.forEach(cause => {
        data.push({
          group: cause,
          value: Math.floor(Math.random() * 50) + 10,
          details: `${cause} analysis for ${timeRange}`
        });
      });
      break;

    case 'vintage':
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
      months.forEach(month => {
        data.push({
          group: month,
          value: Math.floor(Math.random() * 30) + 5,
          details: `${month} vintage analysis`
        });
      });
      break;

    case 'sector':
      const sectors = ['Sector A', 'Sector B', 'Sector C', 'Sector D'];
      sectors.forEach(sector => {
        data.push({
          group: sector,
          value: Math.floor(Math.random() * 40) + 15,
          details: `${sector} performance`
        });
      });
      break;

    case 'supplier':
      const suppliers = ['Supplier 1', 'Supplier 2', 'Supplier 3', 'Supplier 4', 'Supplier 5'];
      suppliers.forEach(supplier => {
        data.push({
          group: supplier,
          value: Math.floor(Math.random() * 35) + 8,
          details: `${supplier} quality metrics`
        });
      });
      break;

    case 'partNum':
      const partNumbers = ['PN-001', 'PN-002', 'PN-003', 'PN-004', 'PN-005'];
      partNumbers.forEach(pn => {
        data.push({
          group: pn,
          value: Math.floor(Math.random() * 25) + 12,
          details: `${pn} failure analysis`
        });
      });
      break;

    case 'category':
      const categories = ['Hardware', 'Software', 'Firmware', 'Mechanical', 'Electrical'];
      categories.forEach(category => {
        data.push({
          group: category,
          value: Math.floor(Math.random() * 45) + 20,
          details: `${category} defect analysis`
        });
      });
      break;

    default:
      // Default data
      for (let i = 1; i <= 5; i++) {
        data.push({
          group: `Item ${i}`,
          value: Math.floor(Math.random() * 50) + 10,
          details: `Analysis item ${i}`
        });
      }
  }

  return data;
}

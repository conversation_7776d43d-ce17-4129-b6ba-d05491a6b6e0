<template>
  <div class="action-items-table">
    <div class="table-header">
      <h3 class="table-title">{{ getTableTitle() }}</h3>
      <div class="table-stats">
  <span
    :class="['alert-count', items.length > 0 ? getAlertClass(items.length) : 'safe']"
  >
    {{ items.length }} alerts
  </span>
</div>
    </div>

    <cv-data-table  :columns="columns" v-if="!isLoading" :expandable="true">
      <template #data>
        <cv-data-table-row
          v-if="items.length === 0"
          :key="'empty'"
        >
          <cv-data-table-cell :colspan="columns.length">
            <div class="empty-message">{{ getEmptyMessage() }}</div>
          </cv-data-table-cell>
        </cv-data-table-row>
        <cv-data-table-row
          v-for="item in items"
          v-else
          :key="item.id"
          :class="getRowClass(item)"
          
        >
          <!-- Priority indicator -->
          <cv-data-table-cell>
            <div class="priority-badge" :class="item.priority.toLowerCase()">
              <span class="priority-indicator" :class="'priority-' + item.priority.toLowerCase()"></span>
              {{ item.priority }}
            </div>
          </cv-data-table-cell>

          <cv-data-table-cell>{{ item.commodity }}</cv-data-table-cell>
          <cv-data-table-cell>{{ item.group }}</cv-data-table-cell>
          <cv-data-table-cell>{{ item.pn }}</cv-data-table-cell>

          <!-- Editable Test Name -->
          <cv-data-table-cell>
            <div class="editable-field">
              <span v-if="!item.isEditingTest">{{ item.editableTest }}</span>
              <input
                v-if="item.isEditingTest"
                v-model="item.editableTest"
                @blur="stopEditingTest(item)"
                @keyup.enter="stopEditingTest(item)"
              />
              <span class="edit-icon" @click="editTest(item)">
                <Edit20 />
              </span>
            </div>
          </cv-data-table-cell>



          <!-- Editable Deadline -->
          <cv-data-table-cell>
            <div class="editable-field">
              <span v-if="!item.isEditingDL">{{ formatDate(item.deadline) }}</span>
              <input
                v-if="item.isEditingDL"
                v-model="item.deadline"
                @blur="stopEditingDL(item)"
                @keyup.enter="stopEditingDL(item)"
                type="date"
              />
              <span class="edit-icon" @click="editDL(item)">
                <Edit20 />
              </span>
            </div>
          </cv-data-table-cell>

          <!-- Dynamic Date Column based on status type -->
          <!-- Monitor Date for monitored items -->
          <cv-data-table-cell v-if="statusType === 'monitored'">
            <span>{{ formatDate(item.monitorDate) }}</span>
          </cv-data-table-cell>

          <!-- Resolution Date for resolved items -->
          <cv-data-table-cell v-if="statusType === 'resolved'">
            <span>{{ formatDate(item.resolutionDate) }}</span>
          </cv-data-table-cell>

          <!-- Date Started for all items -->
          <cv-data-table-cell>
            <span>{{ formatDate(item.dateStarted) }}</span>
          </cv-data-table-cell>

          <!-- Assignee -->
          <cv-data-table-cell>
            <span>{{ item.assignee }}</span>
          </cv-data-table-cell>

          <!-- Tracking -->
          <cv-data-table-cell>
            <cv-button
              size="small"
              kind="tertiary"
              @click="$emit('track-item', item)"
            >
              Tracking
            </cv-button>
          </cv-data-table-cell>
          <template #expandedContent>{{"HI"}}
                        </template>
        </cv-data-table-row>
      </template>
    </cv-data-table>

    <div v-if="isLoading" class="loading-container">
      <cv-loading></cv-loading>
      <span class="loading-text">Loading items...</span>
    </div>

    <div v-if="loadingError" class="error-container">
      <span class="error-text">{{ loadingError }}</span>
    </div>
  </div>
</template>

<script>
import { Edit20 } from '@carbon/icons-vue';

export default {
  name: 'ActionItemsTable',
  components: {
    Edit20
  },
  props: {
    items: {
      type: Array,
      default: () => []
    },
    columns: {
      type: Array,
      default: () => []
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    loadingError: {
      type: String,
      default: null
    },
    statusType: {
      type: String,
      required: true
    }
  },
  methods: {
    getTableTitle() {
      switch (this.statusType) {
        case 'current': return 'Current Items';
        case 'in-progress': return 'In-Progress Items';
        case 'monitored': return 'Monitored Items';
        case 'resolved': return 'Resolved Items';
        default: return 'Action Items';
      }
    },

    getEmptyMessage() {
      switch (this.statusType) {
        case 'current': return 'No current items requiring immediate attention.';
        case 'in-progress': return 'No items currently in progress.';
        case 'monitored': return 'No items being monitored.';
        case 'resolved': return 'No resolved items.';
        default: return 'No items found.';
      }
    },

    getRowClass(item) {
      const classes = ['action-row'];
      if (item.priority) {
        classes.push(`priority-${item.priority.toLowerCase()}`);
      }
      if (item.source === 'pqe') {
        classes.push('pqe-source');
      }
      return classes.join(' ');
    },



    editTest(item) {
      item.isEditingTest = true;
    },

    stopEditingTest(item) {
      item.isEditingTest = false;
      this.$emit('update-item', { id: item.id, test: item.editableTest });
    },

    editDL(item) {
      item.isEditingDL = true;
    },

    stopEditingDL(item) {
      item.isEditingDL = false;
      this.$emit('update-item', { id: item.id, deadline: item.deadline });
    },

    editER(item) {
      item.isEditingER = true;
    },

    stopEditingER(item) {
      item.isEditingER = false;
      this.$emit('update-item', { id: item.id, expectedResolution: item.expectedResolution });
    },

    formatDate(dateString) {
      if (!dateString) return 'N/A';

      if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
        const [year, month, day] = dateString.split('-');
        return `${month}/${day}/${year}`;
      }

      return dateString;
    },

    getAlertClass(count) {
    if (count === 0) return 'safe';
    if (count <= 3) return 'low-flash';
    if (count <= 7) return 'medium-flash';
    return 'high-flash';
  },

    truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.slice(0, maxLength) + '...';
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../styles/carbon-utils";

.action-items-table {
  background-color: #262626;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #333333;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #333333;
  padding: 1rem;
  border-bottom: 1px solid #444444;
}

.table-title {
  color: #f4f4f4;
  font-size: 1.125rem;
  font-weight: 400;
  margin: 0;
}

.table-stats {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.item-count {
  color: #8d8d8d;
  font-size: 0.875rem;
}

/* Priority indicators */
.priority-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.priority-badge.high {
  background-color: rgba(250, 77, 86, 0.2);
  color: #ff8389;
}

.priority-badge.medium {
  background-color: rgba(255, 196, 0, 0.2);
  color: #ffcc00;
}

.priority-badge.low {
  background-color: rgba(66, 190, 101, 0.2);
  color: #6fdc8c;
}

.priority-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.priority-high {
  background-color: #fa4d56;
}

.priority-medium {
  background-color: #ffc400;
}

.priority-low {
  background-color: #42be65;
}

/* Row styling */
.action-row.pqe-source {
  border-left: 3px solid #0f62fe;
}

.action-row.priority-high {
  background-color: rgba(250, 77, 86, 0.05);
}

/* Cell styling */
.editable-field {
  display: flex;
  align-items: center;
}

.edit-icon {
  margin-left: 8px;
  cursor: pointer;
  color: #0f62fe;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.edit-icon:hover {
  opacity: 1;
}

.action-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-text {
  flex-grow: 1;
}

.assignee-cell {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  align-items: flex-start;
}

.progress-container {
  width: 100%;
  padding: 0.5rem 0;
}

.empty-message {
  color: #8d8d8d;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  gap: 1rem;
}

.loading-text {
  color: #0f62fe;
  font-size: 14px;
}

.error-container {
  padding: 1rem;
  text-align: center;
}

.error-text {
  color: #da1e28;
  font-size: 14px;
}

.alert-count {
  font-size: 0.875rem;
  font-weight: bold;
}

.safe {
  color: #6fdc8c; /* Green if zero */
}


.alert-row {
  background-color: rgba(250, 77, 86, 0.1); /* light red */
}
</style>

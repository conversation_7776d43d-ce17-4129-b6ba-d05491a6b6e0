{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\SavedReports\\SavedReports.vue?vue&type=style&index=0&id=ffe6e6f6&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\SavedReports\\SavedReports.vue", "mtime": 1756316093675}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SavedReports.vue"], "names": [], "mappings": ";AAiaA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SavedReports.vue", "sourceRoot": "src/views/SavedReports", "sourcesContent": ["<template>\n  <cv-grid class=\"saved-reports-grid\">\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" @panel-toggle=\"toggleSideNav\" />\n    \n    <cv-row class=\"page-header\">\n      <cv-column>\n        <h1 class=\"page-title\">Saved Reports</h1>\n        <p class=\"page-description\">Manage and schedule your saved analysis reports</p>\n      </cv-column>\n    </cv-row>\n\n    <!-- Reports Management -->\n    <cv-row class=\"reports-section\">\n      <cv-column>\n        <cv-tile class=\"reports-tile\">\n          <div class=\"reports-header\">\n            <h4 class=\"reports-title\">Saved Analysis Jobs</h4>\n            <cv-button @click=\"refreshReports\" kind=\"secondary\" class=\"refresh-button\">\n              Refresh\n            </cv-button>\n          </div>\n\n          <!-- Loading State -->\n          <div v-if=\"isLoading\" class=\"loading-section\">\n            <cv-loading />\n            <p>Loading saved reports...</p>\n          </div>\n\n          <!-- Reports Table -->\n          <div v-else-if=\"savedReports.length > 0\" class=\"reports-table-section\">\n            <cv-data-table\n              :columns=\"reportsColumns\"\n              :data=\"savedReports\"\n              :pagination=\"{ numberOfItems: savedReports.length }\"\n              class=\"reports-table\"\n            >\n              <template v-slot:cell=\"{ cell, row }\">\n                <div v-if=\"cell.header === 'actions'\" class=\"actions-cell\">\n                  <cv-button \n                    @click=\"viewReport(row)\"\n                    kind=\"ghost\"\n                    size=\"sm\"\n                    class=\"action-btn\"\n                  >\n                    View\n                  </cv-button>\n                  <cv-button \n                    @click=\"scheduleReport(row)\"\n                    kind=\"ghost\"\n                    size=\"sm\"\n                    class=\"action-btn\"\n                  >\n                    Schedule\n                  </cv-button>\n                  <cv-button \n                    @click=\"deleteReport(row)\"\n                    kind=\"danger--ghost\"\n                    size=\"sm\"\n                    class=\"action-btn\"\n                  >\n                    Delete\n                  </cv-button>\n                </div>\n                <div v-else-if=\"cell.header === 'status'\" class=\"status-cell\">\n                  <cv-tag \n                    :type=\"getStatusType(cell.value)\"\n                    :label=\"cell.value\"\n                  />\n                </div>\n                <div v-else>\n                  {{ cell.value }}\n                </div>\n              </template>\n            </cv-data-table>\n          </div>\n\n          <!-- No Reports Message -->\n          <div v-else class=\"no-reports\">\n            <p>No saved reports found. Create reports from the Phase 1 analysis page.</p>\n            <cv-button @click=\"goToPhase1\" class=\"create-button\">\n              Go to Phase 1 Analysis\n            </cv-button>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n\n    <!-- Scheduling Modal -->\n    <cv-modal \n      v-model=\"scheduleModalVisible\"\n      :primary-button-disabled=\"!selectedSchedule\"\n      @primary-click=\"saveSchedule\"\n      @secondary-click=\"closeScheduleModal\"\n    >\n      <template v-slot:label>Schedule Report</template>\n      <template v-slot:title>{{ selectedReport.name }}</template>\n      <template v-slot:content>\n        <div class=\"schedule-form\">\n          <div class=\"form-group\">\n            <label class=\"form-label\">Schedule Type:</label>\n            <cv-radio-group v-model=\"selectedSchedule\" class=\"schedule-options\">\n              <cv-radio-button \n                name=\"schedule-type\" \n                label=\"Daily\" \n                value=\"daily\"\n              />\n              <cv-radio-button \n                name=\"schedule-type\" \n                label=\"Weekly\" \n                value=\"weekly\"\n              />\n              <cv-radio-button \n                name=\"schedule-type\" \n                label=\"Monthly\" \n                value=\"monthly\"\n              />\n            </cv-radio-group>\n          </div>\n\n          <div class=\"form-group\" v-if=\"selectedSchedule === 'weekly'\">\n            <label class=\"form-label\">Day of Week:</label>\n            <cv-dropdown v-model=\"scheduleDay\" class=\"schedule-dropdown\">\n              <cv-dropdown-item value=\"monday\">Monday</cv-dropdown-item>\n              <cv-dropdown-item value=\"tuesday\">Tuesday</cv-dropdown-item>\n              <cv-dropdown-item value=\"wednesday\">Wednesday</cv-dropdown-item>\n              <cv-dropdown-item value=\"thursday\">Thursday</cv-dropdown-item>\n              <cv-dropdown-item value=\"friday\">Friday</cv-dropdown-item>\n            </cv-dropdown>\n          </div>\n\n          <div class=\"form-group\" v-if=\"selectedSchedule === 'monthly'\">\n            <label class=\"form-label\">Day of Month:</label>\n            <cv-number-input \n              v-model=\"scheduleDay\" \n              :min=\"1\" \n              :max=\"28\"\n              label=\"Day\"\n            />\n          </div>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Time:</label>\n            <cv-text-input \n              v-model=\"scheduleTime\" \n              placeholder=\"HH:MM (24-hour format)\"\n              pattern=\"^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$\"\n            />\n          </div>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Email Recipients:</label>\n            <cv-text-area \n              v-model=\"emailRecipients\"\n              placeholder=\"Enter email addresses separated by commas\"\n              rows=\"3\"\n            />\n          </div>\n        </div>\n      </template>\n      <template v-slot:primary-button>Save Schedule</template>\n      <template v-slot:secondary-button>Cancel</template>\n    </cv-modal>\n\n    <!-- View Report Modal -->\n    <cv-modal \n      v-model=\"viewModalVisible\"\n      size=\"lg\"\n      @secondary-click=\"closeViewModal\"\n    >\n      <template v-slot:label>Saved Report</template>\n      <template v-slot:title>{{ selectedReport && selectedReport.name }}</template>\n      <template v-slot:content>\n        <div class=\"report-details\" v-if=\"selectedReport\">\n          <div class=\"report-meta\">\n            <p><strong>Created:</strong> {{ formatDate(selectedReport.createdAt) }}</p>\n            <p><strong>Query Type:</strong> {{ (selectedReport.data && selectedReport.data.type) || 'Unknown' }}</p>\n            <p><strong>Parameters:</strong></p>\n            <pre class=\"params-display\">{{ JSON.stringify((selectedReport.data && selectedReport.data.params) || {}, null, 2) }}</pre>\n          </div>\n\n          <div class=\"report-results\" v-if=\"selectedReport.data && selectedReport.data.results\">\n            <h5>Results Summary</h5>\n            <p><strong>Title:</strong> {{ selectedReport.data.results.title }}</p>\n            <p><strong>Data Points:</strong> {{ (selectedReport.data.results.chart_data && selectedReport.data.results.chart_data.length) || 0 }}</p>\n\n            <div class=\"chart-preview\" v-if=\"selectedReport.data.results.chart_data && selectedReport.data.results.chart_data.length > 0\">\n              <h6>Data Preview</h6>\n              <div class=\"data-preview\">\n                <div\n                  v-for=\"(item, index) in selectedReport.data.results.chart_data.slice(0, 5)\"\n                  :key=\"index\"\n                  class=\"data-item\"\n                >\n                  <span class=\"data-label\">{{ item.group }}:</span>\n                  <span class=\"data-value\">{{ item.value }}</span>\n                </div>\n                <div v-if=\"selectedReport.data.results.chart_data.length > 5\" class=\"more-data\">\n                  ... and {{ selectedReport.data.results.chart_data.length - 5 }} more items\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </template>\n      <template v-slot:secondary-button>Close</template>\n    </cv-modal>\n  </cv-grid>\n</template>\n\n<script>\nimport MainHeader from '../../components/MainHeader';\n\nexport default {\n  name: 'SavedReports',\n  components: {\n    MainHeader\n  },\n  data() {\n    return {\n      // UI State\n      expandedSideNav: false,\n      useFixed: true,\n      isLoading: false,\n\n      // Reports Data\n      savedReports: [],\n      reportsColumns: [\n        { header: 'Name', key: 'name' },\n        { header: 'Created', key: 'createdAt' },\n        { header: 'Type', key: 'type' },\n        { header: 'Status', key: 'status' },\n        { header: 'Actions', key: 'actions' }\n      ],\n\n      // Modal States\n      scheduleModalVisible: false,\n      viewModalVisible: false,\n      selectedReport: null,\n\n      // Scheduling Form\n      selectedSchedule: '',\n      scheduleDay: '',\n      scheduleTime: '09:00',\n      emailRecipients: ''\n    };\n  },\n  mounted() {\n    this.loadSavedReports();\n  },\n  methods: {\n    toggleSideNav() {\n      this.expandedSideNav = !this.expandedSideNav;\n    },\n\n    loadSavedReports: function() {\n      var self = this;\n      self.isLoading = true;\n\n      var token = self.$store.getters.getToken;\n      fetch('/api-statit2/get_saved_reports', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify({}),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.savedReports = data.reports.map(function(report) {\n            return {\n              id: report.id,\n              name: report.name,\n              createdAt: self.formatDate(report.createdAt),\n              type: (report.data && report.data.type === 'ai') ? 'AI Query' : 'Dropdown Query',\n              status: report.scheduled ? 'Scheduled' : 'Saved',\n              data: report.data\n            };\n          });\n        }\n      })\n      .catch(function(error) {\n        console.error('Error loading saved reports:', error);\n      })\n      .finally(function() {\n        self.isLoading = false;\n      });\n    },\n\n    refreshReports() {\n      this.loadSavedReports();\n    },\n\n    viewReport(report) {\n      this.selectedReport = report;\n      this.viewModalVisible = true;\n    },\n\n    scheduleReport(report) {\n      this.selectedReport = report;\n      this.selectedSchedule = '';\n      this.scheduleDay = '';\n      this.scheduleTime = '09:00';\n      this.emailRecipients = '';\n      this.scheduleModalVisible = true;\n    },\n\n    deleteReport: function(report) {\n      var self = this;\n      if (!confirm('Are you sure you want to delete \"' + report.name + '\"?')) return;\n\n      var token = self.$store.getters.getToken;\n      fetch('/api-statit2/delete_saved_report', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify({ reportId: report.id }),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.loadSavedReports();\n        }\n      })\n      .catch(function(error) {\n        console.error('Error deleting report:', error);\n      });\n    },\n\n    saveSchedule: function() {\n      var self = this;\n      var token = self.$store.getters.getToken;\n\n      var scheduleData = {\n        reportId: self.selectedReport.id,\n        scheduleType: self.selectedSchedule,\n        scheduleDay: self.scheduleDay,\n        scheduleTime: self.scheduleTime,\n        emailRecipients: self.emailRecipients.split(',').map(function(email) {\n          return email.trim();\n        }).filter(function(email) {\n          return email;\n        })\n      };\n\n      fetch('/api-statit2/schedule_report', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify(scheduleData),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.closeScheduleModal();\n          self.loadSavedReports();\n          alert('Report scheduled successfully!');\n        }\n      })\n      .catch(function(error) {\n        console.error('Error scheduling report:', error);\n        alert('Error scheduling report. Please try again.');\n      });\n    },\n\n    closeScheduleModal() {\n      this.scheduleModalVisible = false;\n      this.selectedReport = null;\n    },\n\n    closeViewModal() {\n      this.viewModalVisible = false;\n      this.selectedReport = null;\n    },\n\n    goToPhase1() {\n      this.$router.push('/phase1');\n    },\n\n    getStatusType(status) {\n      switch (status) {\n        case 'Scheduled': return 'blue';\n        case 'Saved': return 'green';\n        default: return 'gray';\n      }\n    },\n\n    formatDate(dateString) {\n      if (!dateString) return 'N/A';\n      return new Date(dateString).toLocaleDateString() + ' ' + new Date(dateString).toLocaleTimeString();\n    },\n\n    handleResponse(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          this.$router.push('/');\n        }\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      return response.json();\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../styles/carbon-utils\";\n\n.saved-reports-grid {\n  min-height: 100vh;\n  background-color: #161616;\n  padding: 0 $spacing-05;\n}\n\n.page-header {\n  margin-top: $spacing-05;\n  margin-bottom: $spacing-04;\n}\n\n.page-title {\n  @include carbon--type-style('productive-heading-03');\n  color: $text-01;\n  margin-bottom: $spacing-02;\n}\n\n.page-description {\n  @include carbon--type-style('body-short-01');\n  color: $text-02;\n}\n\n.reports-section {\n  margin-bottom: $spacing-05;\n}\n\n.reports-tile {\n  padding: $spacing-05;\n  background-color: #262626;\n}\n\n.reports-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $spacing-05;\n}\n\n.reports-title {\n  @include carbon--type-style('productive-heading-02');\n  color: $text-01;\n  margin: 0;\n}\n\n.loading-section {\n  text-align: center;\n  padding: $spacing-07;\n  \n  p {\n    @include carbon--type-style('body-short-01');\n    color: $text-02;\n    margin-top: $spacing-03;\n  }\n}\n\n.reports-table-section {\n  .reports-table {\n    margin-bottom: $spacing-05;\n  }\n\n  .actions-cell {\n    display: flex;\n    gap: $spacing-02;\n  }\n\n  .action-btn {\n    min-width: 60px;\n  }\n\n  .status-cell {\n    display: flex;\n    align-items: center;\n  }\n}\n\n.no-reports {\n  text-align: center;\n  padding: $spacing-07;\n  \n  p {\n    @include carbon--type-style('body-long-01');\n    color: $text-02;\n    margin-bottom: $spacing-05;\n  }\n}\n\n.schedule-form {\n  .form-group {\n    margin-bottom: $spacing-05;\n  }\n\n  .form-label {\n    @include carbon--type-style('label-01');\n    color: $text-01;\n    display: block;\n    margin-bottom: $spacing-03;\n  }\n\n  .schedule-options {\n    display: flex;\n    gap: $spacing-05;\n  }\n\n  .schedule-dropdown {\n    width: 100%;\n  }\n}\n\n.report-details {\n  .report-meta {\n    margin-bottom: $spacing-05;\n    \n    p {\n      @include carbon--type-style('body-short-01');\n      color: $text-01;\n      margin-bottom: $spacing-02;\n    }\n\n    .params-display {\n      background-color: $ui-01;\n      padding: $spacing-03;\n      border-radius: $spacing-02;\n      font-family: 'IBM Plex Mono', monospace;\n      font-size: 12px;\n      overflow-x: auto;\n    }\n  }\n\n  .report-results {\n    h5, h6 {\n      @include carbon--type-style('productive-heading-01');\n      color: $text-01;\n      margin-bottom: $spacing-03;\n    }\n\n    .chart-preview {\n      margin-top: $spacing-04;\n      \n      .data-preview {\n        background-color: $ui-01;\n        padding: $spacing-04;\n        border-radius: $spacing-02;\n        \n        .data-item {\n          display: flex;\n          justify-content: space-between;\n          margin-bottom: $spacing-02;\n          \n          .data-label {\n            font-weight: 600;\n          }\n        }\n\n        .more-data {\n          @include carbon--type-style('caption-01');\n          color: $text-02;\n          font-style: italic;\n          margin-top: $spacing-02;\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}
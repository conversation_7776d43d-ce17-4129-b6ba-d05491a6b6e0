{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\MainHeader\\MainHeader.vue?vue&type=style&index=0&id=7df53362&scoped=true&lang=css", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\components\\MainHeader\\MainHeader.vue", "mtime": 1756310940853}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQovKiBBZGQgc3BlY2lmaWMgc3R5bGVzIGZvciB0aGUgaGVhZGVyIGhlcmUsIGlmIG5lZWRlZCAqLw0K"}, {"version": 3, "sources": ["MainHeader.vue"], "names": [], "mappings": ";AA4DA", "file": "MainHeader.vue", "sourceRoot": "src/components/MainHeader", "sourcesContent": ["<template>\r\n  <cv-header aria-label=\"Carbon header\">\r\n    <cv-header-menu-button\r\n      aria-label=\"Header menu\"\r\n      aria-controls=\"side-nav\"\r\n      :active=\"expandedSideNav\"\r\n      @click=\"$emit('panel-toggle')\"\r\n    />\r\n    <cv-skip-to-content href=\"#main-content\">Skip to content</cv-skip-to-content>\r\n\r\n    <!-- Make the name clickable by adding a click event -->\r\n    <cv-header-name href=\"javascript:void(0)\" prefix=\"IBM\" @click=\"navigateTo('/home-page')\">STATIT 2.0</cv-header-name>\r\n\r\n    <template v-slot:left-panels>\r\n      <cv-side-nav id=\"side-nav\" @panel-resize=\"onPanelResize\" :rail=\"true\" :fixed=\"useFixed\" :expanded=\"expandedSideNav\">\r\n        <cv-side-nav-items>\r\n          <cv-side-nav-menu title=\"Menu\">\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/commodity-overview')\">Commodity Page</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/system-level-fpy')\">System Level FPY</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/pn-analysis')\">PN Analysis</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/phase1')\">Phase 1</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/validations')\">Validations</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/action-tracker')\">Action Tracker</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/pqe-dashboard')\">PQE Dashboard</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/watsonX')\">Watson Assistant</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/XFactorOverall')\">X-Factor Overall</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/x-factor-analysis')\">Part Group Analysis</cv-side-nav-menu-item>\r\n            <cv-side-nav-menu-item @click=\"navigateTo('/metis-xfactors')\">Metis XFactors</cv-side-nav-menu-item>\r\n          </cv-side-nav-menu>\r\n        </cv-side-nav-items>\r\n      </cv-side-nav>\r\n    </template>\r\n  </cv-header>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'MainHeader',\r\n  props: {\r\n    expandedSideNav: {\r\n      type: Boolean,\r\n      default: true,\r\n    },\r\n    useFixed: {\r\n      type: Boolean,\r\n      default: false,\r\n    }\r\n  },\r\n  methods: {\r\n    navigateTo(route) {\r\n      this.$router.push(route); // Navigate to the specified route\r\n    },\r\n    onPanelResize() {\r\n      // Handle panel resize event if needed\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n/* Add specific styles for the header here, if needed */\r\n</style>\r\n"]}]}
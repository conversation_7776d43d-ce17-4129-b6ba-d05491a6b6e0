{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=style&index=0&id=2da97105&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756316352837}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Phase1.vue"], "names": [], "mappings": ";AA4nBA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Phase1.vue", "sourceRoot": "src/views/Phase1", "sourcesContent": ["<template>\n  <cv-grid class=\"phase1-grid\">\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" @panel-toggle=\"toggleSideNav\" />\n    \n    <cv-row class=\"page-header\">\n      <cv-column>\n        <h1 class=\"page-title\">Phase 1 Analysis</h1>\n        <p class=\"page-description\">Advanced data analysis with flexible querying capabilities</p>\n      </cv-column>\n    </cv-row>\n\n    <!-- Control Panel -->\n    <cv-row class=\"control-panel\">\n      <cv-column :lg=\"8\" :md=\"6\" :sm=\"4\">\n        <cv-tile class=\"controls-tile\">\n          <div class=\"controls-header\">\n            <h4 class=\"controls-title\">Analysis Controls</h4>\n\n            <!-- Query Method Selection -->\n            <cv-radio-group\n              v-model=\"queryMethod\"\n              @change=\"handleQueryMethodChange\"\n              class=\"query-method-group\"\n            >\n              <cv-radio-button\n                name=\"query-method\"\n                label=\"Dropdown Builder\"\n                value=\"dropdown\"\n              />\n              <cv-radio-button\n                name=\"query-method\"\n                label=\"AI Prompt\"\n                value=\"ai\"\n              />\n            </cv-radio-group>\n          </div>\n\n          <!-- Dropdown Query Builder -->\n          <div v-if=\"queryMethod === 'dropdown'\" class=\"dropdown-builder\">\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">View By:</label>\n                <cv-dropdown\n                  v-model=\"viewBy\"\n                  @change=\"handleViewByChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                  <cv-dropdown-item value=\"partNum\">Part Number</cv-dropdown-item>\n                  <cv-dropdown-item value=\"category\">Category</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"timeRange\"\n                  @change=\"handleTimeRangeChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"1month\">1 Month</cv-dropdown-item>\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"12month\">12 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"custom\">Custom Range</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Process:</label>\n                <cv-dropdown\n                  v-model=\"selectedProcess\"\n                  @change=\"handleProcessChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Processes</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FAB\">FAB</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FUL\">FUL</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n            </div>\n\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Part Group:</label>\n                <cv-dropdown\n                  v-model=\"selectedPartGroup\"\n                  @change=\"handlePartGroupChange\"\n                  class=\"filter-dropdown\"\n                  :disabled=\"!partGroupOptions.length\"\n                >\n                  <cv-dropdown-item value=\"all\">All Part Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in partGroupOptions\"\n                    :key=\"group.value\"\n                    :value=\"group.value\"\n                  >\n                    {{ group.label }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\" v-if=\"timeRange === 'custom'\">\n                <label class=\"filter-label\">Date Range:</label>\n                <cv-date-picker\n                  v-model=\"customDateRange\"\n                  label=\"Select Date Range\"\n                  kind=\"range\"\n                  :cal-options=\"calOptions\"\n                  placeholder=\"yyyy-mm-dd\"\n                  @change=\"handleDateRangeChange\"\n                />\n              </div>\n\n              <div class=\"filter-group action-group\">\n                <cv-button\n                  @click=\"executeDropdownQuery\"\n                  :disabled=\"isLoading\"\n                  class=\"execute-button\"\n                >\n                  {{ isLoading ? 'Loading...' : 'Execute Query' }}\n                </cv-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- AI Prompt Interface -->\n          <div v-if=\"queryMethod === 'ai'\" class=\"ai-prompt-section\">\n            <div class=\"prompt-input-group\">\n              <label class=\"control-label\">Natural Language Query:</label>\n              <cv-text-area\n                v-model=\"aiPrompt\"\n                placeholder=\"Example: Show me the top 5 root causes for FAB process in the last 3 months with failure rates above 2%\"\n                rows=\"3\"\n                class=\"prompt-textarea\"\n              />\n            </div>\n            <div class=\"prompt-controls\">\n              <cv-button\n                @click=\"executeAiQuery\"\n                :disabled=\"isLoading || !aiPrompt.trim()\"\n                class=\"execute-button\"\n              >\n                {{ isLoading ? 'Processing...' : 'Ask AI' }}\n              </cv-button>\n              <cv-button\n                kind=\"secondary\"\n                @click=\"clearAiPrompt\"\n                class=\"clear-button\"\n              >\n                Clear\n              </cv-button>\n            </div>\n          </div>\n        </cv-tile>\n      </cv-column>\n\n      <!-- Action Panel -->\n      <cv-column :lg=\"4\" :md=\"2\" :sm=\"4\" v-if=\"hasResults\">\n        <cv-tile class=\"action-tile\">\n          <h5 class=\"action-title\">Actions</h5>\n          <div class=\"action-buttons\">\n            <cv-button\n              @click=\"saveJob\"\n              :disabled=\"isLoading\"\n              class=\"action-button\"\n              kind=\"secondary\"\n            >\n              Save Job\n            </cv-button>\n            <cv-button\n              @click=\"exportData('csv')\"\n              :disabled=\"isLoading || !chartData.length\"\n              class=\"action-button\"\n              kind=\"tertiary\"\n            >\n              Export CSV\n            </cv-button>\n            <cv-button\n              @click=\"exportData('pdf')\"\n              :disabled=\"isLoading || !chartData.length\"\n              class=\"action-button\"\n              kind=\"tertiary\"\n            >\n              Export PDF\n            </cv-button>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n\n    <!-- Results Section -->\n    <cv-row class=\"results-section\" v-if=\"hasResults\">\n      <cv-column>\n        <cv-tile class=\"results-tile\">\n          <div class=\"results-header\">\n            <h4 class=\"results-title\">{{ resultsTitle }}</h4>\n            <div class=\"results-meta\" v-if=\"!isLoading && chartData.length > 0\">\n              <span class=\"data-count\">{{ chartData.length }} data points</span>\n              <span class=\"query-time\">{{ queryExecutionTime }}</span>\n            </div>\n          </div>\n\n          <!-- Loading State -->\n          <div v-if=\"isLoading\" class=\"loading-section\">\n            <cv-loading />\n            <p>Processing your query...</p>\n          </div>\n\n          <!-- Chart Display -->\n          <div v-else-if=\"chartData.length > 0\" class=\"chart-section\">\n            <div class=\"chart-container\">\n              <BarChart\n                v-if=\"chartType === 'bar'\"\n                :data=\"chartData\"\n                @bar-clicked=\"handleBarClick\"\n                :loading=\"false\"\n                class=\"chart-component\"\n              />\n              <LineChart\n                v-if=\"chartType === 'line'\"\n                :data=\"chartData\"\n                @point-clicked=\"handlePointClick\"\n                :loading=\"false\"\n                class=\"chart-component\"\n              />\n            </div>\n\n            <!-- Data Table -->\n            <div class=\"data-table-section\">\n              <h5>Data Summary</h5>\n              <cv-data-table\n                :columns=\"tableColumns\"\n                :data=\"tableData\"\n                :pagination=\"{ numberOfItems: chartData.length }\"\n                class=\"results-table\"\n              >\n                <template v-slot:cell=\"{ cell }\">\n                  <div v-if=\"cell.header === 'value'\" class=\"value-cell\">\n                    {{ formatValue(cell.value) }}\n                  </div>\n                  <div v-else>\n                    {{ cell.value }}\n                  </div>\n                </template>\n              </cv-data-table>\n            </div>\n          </div>\n\n          <!-- AI Response Display -->\n          <div v-if=\"aiResponse\" class=\"ai-response-section\">\n            <h5>AI Analysis:</h5>\n            <div class=\"ai-response-content\">\n              {{ aiResponse }}\n            </div>\n          </div>\n\n          <!-- No Results Message -->\n          <div v-else-if=\"!isLoading\" class=\"no-results\">\n            <p>No data found for the current query. Try adjusting your filters or prompt.</p>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n  </cv-grid>\n</template>\n\n<script>\nimport MainHeader from '../../components/MainHeader';\nimport BarChart from '../../components/BarChart';\nimport LineChart from '../../components/LineChart';\n\nexport default {\n  name: 'Phase1Page',\n  components: {\n    MainHeader,\n    BarChart,\n    LineChart\n  },\n  data() {\n    return {\n      // UI State\n      expandedSideNav: false,\n      useFixed: true,\n      isLoading: false,\n      hasResults: false,\n\n      // Query Method\n      queryMethod: 'dropdown',\n\n      // Dropdown Query Builder\n      viewBy: 'rootCause',\n      timeRange: '3month',\n      customDateRange: '',\n      selectedProcess: 'all',\n      selectedPartGroup: 'all',\n      partGroupOptions: [],\n\n      // AI Prompt\n      aiPrompt: '',\n      aiResponse: '',\n\n      // Results\n      chartData: [],\n      chartType: 'bar',\n      resultsTitle: '',\n      queryExecutionTime: '',\n\n      // Table Data\n      tableColumns: [\n        { header: 'Category', key: 'group' },\n        { header: 'Value', key: 'value' },\n        { header: 'Details', key: 'details' }\n      ],\n\n      // Current Job Data\n      currentJobData: null,\n\n      // Options\n      calOptions: { dateFormat: \"Y-m-d\" }\n    };\n  },\n  computed: {\n    tableData: function() {\n      var self = this;\n      return self.chartData.map(function(item, index) {\n        return {\n          id: index,\n          group: item.group,\n          value: item.value,\n          details: item.details || 'N/A'\n        };\n      });\n    }\n  },\n  mounted() {\n    this.loadPartGroupOptions();\n  },\n  methods: {\n    toggleSideNav: function() {\n      this.expandedSideNav = !this.expandedSideNav;\n    },\n\n    handleQueryMethodChange: function() {\n      this.clearResults();\n    },\n\n    handleViewByChange: function() {\n      // Auto-execute if we have enough parameters\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleTimeRangeChange: function() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleDateRangeChange: function() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleProcessChange: function() {\n      this.loadPartGroupOptions();\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handlePartGroupChange: function() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    loadPartGroupOptions: function() {\n      var self = this;\n      var token = self.$store.getters.getToken;\n\n      fetch('/api-statit2/get_phase1_part_groups', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify({\n          process: self.selectedProcess\n        }),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.partGroupOptions = data.part_groups || [];\n        }\n      })\n      .catch(function(error) {\n        console.error('Error loading part group options:', error);\n      });\n    },\n\n    executeDropdownQuery: function() {\n      var self = this;\n      if (self.isLoading) return;\n\n      self.isLoading = true;\n      self.hasResults = true;\n      var startTime = Date.now();\n\n      var token = self.$store.getters.getToken;\n      var queryParams = {\n        viewBy: self.viewBy,\n        timeRange: self.timeRange,\n        customDateRange: self.timeRange === 'custom' ? self.customDateRange : null,\n        process: self.selectedProcess,\n        partGroup: self.selectedPartGroup\n      };\n\n      fetch('/api-statit2/execute_phase1_dropdown_query', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify(queryParams),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.chartData = data.chart_data || [];\n          self.chartType = data.chart_type || 'bar';\n          self.resultsTitle = data.title || 'Analysis Results';\n          self.aiResponse = '';\n\n          // Store current job data\n          self.currentJobData = {\n            type: 'dropdown',\n            params: queryParams,\n            results: data,\n            timestamp: new Date().toISOString()\n          };\n        }\n      })\n      .catch(function(error) {\n        console.error('Error executing dropdown query:', error);\n      })\n      .finally(function() {\n        self.isLoading = false;\n        var endTime = Date.now();\n        self.queryExecutionTime = 'Executed in ' + (endTime - startTime) + 'ms';\n      });\n    },\n\n    executeAiQuery: function() {\n      var self = this;\n      if (self.isLoading || !self.aiPrompt.trim()) return;\n\n      self.isLoading = true;\n      self.hasResults = true;\n      var startTime = Date.now();\n\n      var token = self.$store.getters.getToken;\n\n      fetch('/api-statit2/execute_phase1_ai_query', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify({\n          prompt: self.aiPrompt.trim()\n        }),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          self.chartData = data.chart_data || [];\n          self.chartType = data.chart_type || 'bar';\n          self.resultsTitle = data.title || 'AI Analysis Results';\n          self.aiResponse = data.ai_response || '';\n\n          // Store current job data\n          self.currentJobData = {\n            type: 'ai',\n            params: { prompt: self.aiPrompt.trim() },\n            results: data,\n            timestamp: new Date().toISOString()\n          };\n        }\n      })\n      .catch(function(error) {\n        console.error('Error executing AI query:', error);\n      })\n      .finally(function() {\n        self.isLoading = false;\n        var endTime = Date.now();\n        self.queryExecutionTime = 'Executed in ' + (endTime - startTime) + 'ms';\n      });\n    },\n\n    clearAiPrompt: function() {\n      this.aiPrompt = '';\n      this.clearResults();\n    },\n\n    clearResults: function() {\n      this.chartData = [];\n      this.aiResponse = '';\n      this.hasResults = false;\n      this.resultsTitle = '';\n    },\n\n    handleResponse: function(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Handle session expiration\n          this.$router.push('/');\n        }\n        throw new Error('HTTP error! status: ' + response.status);\n      }\n      return response.json();\n    },\n\n    handleBarClick: function(data) {\n      console.log('Bar clicked:', data);\n      // Handle bar chart interactions\n    },\n\n    handlePointClick: function(data) {\n      console.log('Point clicked:', data);\n      // Handle line chart interactions\n    },\n\n    formatValue: function(value) {\n      if (typeof value === 'number') {\n        return value.toLocaleString();\n      }\n      return value;\n    },\n\n    exportData: function(format) {\n      var self = this;\n      if (!self.chartData.length) return;\n\n      var token = self.$store.getters.getToken;\n      var exportData = {\n        format: format,\n        data: self.chartData,\n        title: self.resultsTitle,\n        queryParams: (self.currentJobData && self.currentJobData.params) || {},\n        timestamp: new Date().toISOString()\n      };\n\n      fetch('/api-statit2/export_phase1_data', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify(exportData),\n      })\n      .then(function(response) {\n        if (response.ok) {\n          return response.blob();\n        }\n        throw new Error('Export failed');\n      })\n      .then(function(blob) {\n        var url = window.URL.createObjectURL(blob);\n        var a = document.createElement('a');\n        a.href = url;\n        a.download = 'phase1_analysis_' + Date.now() + '.' + format;\n        document.body.appendChild(a);\n        a.click();\n        window.URL.revokeObjectURL(url);\n        document.body.removeChild(a);\n      })\n      .catch(function(error) {\n        console.error('Error exporting data:', error);\n      });\n    },\n\n    saveJob: function() {\n      var self = this;\n      if (!self.currentJobData) return;\n\n      var jobName = prompt('Enter a name for this job:');\n      if (!jobName) return;\n\n      var token = self.$store.getters.getToken;\n      var saveData = {\n        name: jobName,\n        jobData: self.currentJobData,\n        createdAt: new Date().toISOString()\n      };\n\n      fetch('/api-statit2/save_phase1_job', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n          Authorization: 'Bearer ' + token,\n        },\n        body: JSON.stringify(saveData),\n      })\n      .then(function(response) {\n        return self.handleResponse(response);\n      })\n      .then(function(data) {\n        if (data && data.status_res === 'success') {\n          alert('Job \"' + jobName + '\" saved successfully!');\n          // Navigate to saved reports tab\n          self.$router.push('/saved-reports');\n        }\n      })\n      .catch(function(error) {\n        console.error('Error saving job:', error);\n        alert('Error saving job. Please try again.');\n      });\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../styles/carbon-utils\";\n\n.phase1-grid {\n  min-height: 100vh;\n  background-color: #161616;\n  padding: 0 $spacing-05;\n}\n\n.page-header {\n  margin-top: $spacing-05;\n  margin-bottom: $spacing-04;\n}\n\n.page-title {\n  @include carbon--type-style('productive-heading-03');\n  color: $text-01;\n  margin-bottom: $spacing-02;\n}\n\n.page-description {\n  @include carbon--type-style('body-short-01');\n  color: $text-02;\n}\n\n.control-panel {\n  margin-bottom: $spacing-04;\n}\n\n.controls-tile {\n  padding: $spacing-04;\n  height: fit-content;\n  background-color: #262626;\n}\n\n.controls-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $spacing-04;\n}\n\n.controls-title {\n  @include carbon--type-style('productive-heading-01');\n  color: $text-01;\n  margin: 0;\n}\n\n.control-label {\n  @include carbon--type-style('label-01');\n  color: $text-01;\n  display: block;\n  margin-bottom: $spacing-02;\n}\n\n.query-method-group {\n  display: flex;\n  gap: $spacing-04;\n}\n\n.action-tile {\n  padding: $spacing-04;\n  height: fit-content;\n  background-color: #262626;\n}\n\n.action-title {\n  @include carbon--type-style('productive-heading-01');\n  color: $text-01;\n  margin-bottom: $spacing-03;\n}\n\n.action-buttons {\n  display: flex;\n  flex-direction: column;\n  gap: $spacing-03;\n}\n\n.action-button {\n  width: 100%;\n}\n\n.dropdown-builder {\n  .filter-row {\n    display: grid;\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: $spacing-04;\n    margin-bottom: $spacing-03;\n  }\n\n  .filter-group {\n    min-width: 0;\n\n    &.action-group {\n      display: flex;\n      align-items: end;\n    }\n  }\n\n  .filter-label {\n    @include carbon--type-style('label-01');\n    color: $text-01;\n    display: block;\n    margin-bottom: $spacing-02;\n  }\n\n  .filter-dropdown {\n    width: 100%;\n  }\n}\n\n.ai-prompt-section {\n  .prompt-input-group {\n    margin-bottom: $spacing-03;\n  }\n\n  .prompt-textarea {\n    width: 100%;\n  }\n\n  .prompt-controls {\n    display: flex;\n    gap: $spacing-03;\n  }\n}\n\n.execute-button {\n  min-width: 120px;\n}\n\n.clear-button {\n  min-width: 80px;\n}\n\n.results-section {\n  margin-bottom: $spacing-05;\n}\n\n.results-tile {\n  padding: $spacing-04;\n  background-color: #262626;\n}\n\n.results-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: $spacing-04;\n}\n\n.results-title {\n  @include carbon--type-style('productive-heading-02');\n  color: $text-01;\n  margin: 0;\n}\n\n.results-meta {\n  display: flex;\n  gap: $spacing-04;\n\n  .data-count, .query-time {\n    @include carbon--type-style('caption-01');\n    color: $text-02;\n  }\n}\n\n.loading-section {\n  text-align: center;\n  padding: $spacing-05;\n\n  p {\n    @include carbon--type-style('body-short-01');\n    color: $text-02;\n    margin-top: $spacing-03;\n  }\n}\n\n.chart-section {\n  .chart-container {\n    height: 350px;\n    margin-bottom: $spacing-05;\n  }\n\n  .chart-component {\n    height: 100%;\n  }\n}\n\n.data-table-section {\n  h5 {\n    @include carbon--type-style('productive-heading-01');\n    color: $text-01;\n    margin-bottom: $spacing-03;\n  }\n\n  .results-table {\n    max-height: 300px;\n    overflow-y: auto;\n  }\n\n  .value-cell {\n    font-weight: 600;\n    color: $text-01;\n  }\n}\n\n.ai-response-section {\n  margin-top: $spacing-04;\n  padding: $spacing-04;\n  background-color: $ui-01;\n  border-radius: $spacing-02;\n\n  h5 {\n    @include carbon--type-style('productive-heading-01');\n    color: $text-01;\n    margin-bottom: $spacing-03;\n  }\n}\n\n.ai-response-content {\n  @include carbon--type-style('body-short-01');\n  color: $text-01;\n  line-height: 1.5;\n  white-space: pre-wrap;\n}\n\n.no-results {\n  text-align: center;\n  padding: $spacing-05;\n\n  p {\n    @include carbon--type-style('body-short-01');\n    color: $text-02;\n  }\n}\n</style>\n"]}]}
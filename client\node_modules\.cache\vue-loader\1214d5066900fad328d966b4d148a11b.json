{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=style&index=0&id=2da97105&scoped=true&lang=scss", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756311176776}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\@vue\\cli-service\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Phase1.vue"], "names": [], "mappings": ";AA6aA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "Phase1.vue", "sourceRoot": "src/views/Phase1", "sourcesContent": ["<template>\n  <cv-grid class=\"phase1-grid\">\n    <MainHeader :expandedSideNav=\"expandedSideNav\" :useFixed=\"useFixed\" @panel-toggle=\"toggleSideNav\" />\n    \n    <cv-row class=\"page-header\">\n      <cv-column>\n        <h1 class=\"page-title\">Phase 1 Analysis</h1>\n        <p class=\"page-description\">Advanced data analysis with flexible querying capabilities</p>\n      </cv-column>\n    </cv-row>\n\n    <!-- Control Panel -->\n    <cv-row class=\"control-panel\">\n      <cv-column>\n        <cv-tile class=\"controls-tile\">\n          <h4 class=\"controls-title\">Analysis Controls</h4>\n          \n          <!-- Query Method Selection -->\n          <div class=\"control-section\">\n            <label class=\"control-label\">Query Method:</label>\n            <cv-radio-group \n              v-model=\"queryMethod\" \n              @change=\"handleQueryMethodChange\"\n              class=\"query-method-group\"\n            >\n              <cv-radio-button \n                name=\"query-method\" \n                label=\"Dropdown Builder\" \n                value=\"dropdown\"\n              />\n              <cv-radio-button \n                name=\"query-method\" \n                label=\"AI Prompt\" \n                value=\"ai\"\n              />\n            </cv-radio-group>\n          </div>\n\n          <!-- Dropdown Query Builder -->\n          <div v-if=\"queryMethod === 'dropdown'\" class=\"dropdown-builder\">\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">View By:</label>\n                <cv-dropdown\n                  v-model=\"viewBy\"\n                  @change=\"handleViewByChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"rootCause\">Root Cause</cv-dropdown-item>\n                  <cv-dropdown-item value=\"vintage\">Vintage</cv-dropdown-item>\n                  <cv-dropdown-item value=\"sector\">Sector</cv-dropdown-item>\n                  <cv-dropdown-item value=\"supplier\">Supplier</cv-dropdown-item>\n                  <cv-dropdown-item value=\"partNum\">Part Number</cv-dropdown-item>\n                  <cv-dropdown-item value=\"category\">Category</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Time Range:</label>\n                <cv-dropdown\n                  v-model=\"timeRange\"\n                  @change=\"handleTimeRangeChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"1month\">1 Month</cv-dropdown-item>\n                  <cv-dropdown-item value=\"3month\">3 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"6month\">6 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"12month\">12 Months</cv-dropdown-item>\n                  <cv-dropdown-item value=\"custom\">Custom Range</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\" v-if=\"timeRange === 'custom'\">\n                <label class=\"filter-label\">Date Range:</label>\n                <cv-date-picker\n                  v-model=\"customDateRange\"\n                  label=\"Select Date Range\"\n                  kind=\"range\"\n                  :cal-options=\"calOptions\"\n                  placeholder=\"yyyy-mm-dd\"\n                  @change=\"handleDateRangeChange\"\n                />\n              </div>\n            </div>\n\n            <div class=\"filter-row\">\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Process:</label>\n                <cv-dropdown\n                  v-model=\"selectedProcess\"\n                  @change=\"handleProcessChange\"\n                  class=\"filter-dropdown\"\n                >\n                  <cv-dropdown-item value=\"all\">All Processes</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FAB\">FAB</cv-dropdown-item>\n                  <cv-dropdown-item value=\"FUL\">FUL</cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <label class=\"filter-label\">Part Group:</label>\n                <cv-dropdown\n                  v-model=\"selectedPartGroup\"\n                  @change=\"handlePartGroupChange\"\n                  class=\"filter-dropdown\"\n                  :disabled=\"!partGroupOptions.length\"\n                >\n                  <cv-dropdown-item value=\"all\">All Part Groups</cv-dropdown-item>\n                  <cv-dropdown-item\n                    v-for=\"group in partGroupOptions\"\n                    :key=\"group.value\"\n                    :value=\"group.value\"\n                  >\n                    {{ group.label }}\n                  </cv-dropdown-item>\n                </cv-dropdown>\n              </div>\n\n              <div class=\"filter-group\">\n                <cv-button \n                  @click=\"executeDropdownQuery\"\n                  :disabled=\"isLoading\"\n                  class=\"execute-button\"\n                >\n                  {{ isLoading ? 'Loading...' : 'Execute Query' }}\n                </cv-button>\n              </div>\n            </div>\n          </div>\n\n          <!-- AI Prompt Interface -->\n          <div v-if=\"queryMethod === 'ai'\" class=\"ai-prompt-section\">\n            <div class=\"prompt-input-group\">\n              <label class=\"control-label\">Natural Language Query:</label>\n              <cv-text-area\n                v-model=\"aiPrompt\"\n                placeholder=\"Example: Show me the top 5 root causes for FAB process in the last 3 months with failure rates above 2%\"\n                rows=\"4\"\n                class=\"prompt-textarea\"\n              />\n            </div>\n            <div class=\"prompt-controls\">\n              <cv-button \n                @click=\"executeAiQuery\"\n                :disabled=\"isLoading || !aiPrompt.trim()\"\n                class=\"execute-button\"\n              >\n                {{ isLoading ? 'Processing...' : 'Ask AI' }}\n              </cv-button>\n              <cv-button \n                kind=\"secondary\"\n                @click=\"clearAiPrompt\"\n                class=\"clear-button\"\n              >\n                Clear\n              </cv-button>\n            </div>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n\n    <!-- Results Section -->\n    <cv-row class=\"results-section\" v-if=\"hasResults\">\n      <cv-column>\n        <cv-tile class=\"results-tile\">\n          <h4 class=\"results-title\">{{ resultsTitle }}</h4>\n          \n          <!-- Loading State -->\n          <div v-if=\"isLoading\" class=\"loading-section\">\n            <cv-loading />\n            <p>Processing your query...</p>\n          </div>\n\n          <!-- Chart Display -->\n          <div v-else-if=\"chartData.length > 0\" class=\"chart-section\">\n            <BarChart \n              v-if=\"chartType === 'bar'\"\n              :data=\"chartData\" \n              @bar-clicked=\"handleBarClick\" \n              :loading=\"isLoading\"\n            />\n            <LineChart \n              v-if=\"chartType === 'line'\"\n              :data=\"chartData\" \n              @point-clicked=\"handlePointClick\" \n              :loading=\"isLoading\"\n            />\n          </div>\n\n          <!-- AI Response Display -->\n          <div v-if=\"aiResponse\" class=\"ai-response-section\">\n            <h5>AI Analysis:</h5>\n            <div class=\"ai-response-content\">\n              {{ aiResponse }}\n            </div>\n          </div>\n\n          <!-- No Results Message -->\n          <div v-else-if=\"!isLoading\" class=\"no-results\">\n            <p>No data found for the current query. Try adjusting your filters or prompt.</p>\n          </div>\n        </cv-tile>\n      </cv-column>\n    </cv-row>\n  </cv-grid>\n</template>\n\n<script>\nimport MainHeader from '../../components/MainHeader';\nimport BarChart from '../../components/BarChart';\nimport LineChart from '../../components/LineChart';\n\nexport default {\n  name: 'Phase1Page',\n  components: {\n    MainHeader,\n    BarChart,\n    LineChart\n  },\n  data() {\n    return {\n      // UI State\n      expandedSideNav: false,\n      useFixed: true,\n      isLoading: false,\n      hasResults: false,\n\n      // Query Method\n      queryMethod: 'dropdown',\n\n      // Dropdown Query Builder\n      viewBy: 'rootCause',\n      timeRange: '3month',\n      customDateRange: '',\n      selectedProcess: 'all',\n      selectedPartGroup: 'all',\n      partGroupOptions: [],\n\n      // AI Prompt\n      aiPrompt: '',\n      aiResponse: '',\n\n      // Results\n      chartData: [],\n      chartType: 'bar',\n      resultsTitle: '',\n\n      // Options\n      calOptions: { dateFormat: \"Y-m-d\" }\n    };\n  },\n  mounted() {\n    this.loadPartGroupOptions();\n  },\n  methods: {\n    toggleSideNav() {\n      this.expandedSideNav = !this.expandedSideNav;\n    },\n\n    handleQueryMethodChange() {\n      this.clearResults();\n    },\n\n    handleViewByChange() {\n      // Auto-execute if we have enough parameters\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleTimeRangeChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleDateRangeChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handleProcessChange() {\n      this.loadPartGroupOptions();\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    handlePartGroupChange() {\n      if (this.queryMethod === 'dropdown') {\n        this.executeDropdownQuery();\n      }\n    },\n\n    async loadPartGroupOptions() {\n      try {\n        const token = this.$store.getters.getToken;\n        const response = await fetch('/api-statit2/get_phase1_part_groups', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({ \n            process: this.selectedProcess \n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.partGroupOptions = data.part_groups || [];\n        }\n      } catch (error) {\n        console.error('Error loading part group options:', error);\n      }\n    },\n\n    async executeDropdownQuery() {\n      if (this.isLoading) return;\n\n      this.isLoading = true;\n      this.hasResults = true;\n\n      try {\n        const token = this.$store.getters.getToken;\n        const queryParams = {\n          viewBy: this.viewBy,\n          timeRange: this.timeRange,\n          customDateRange: this.timeRange === 'custom' ? this.customDateRange : null,\n          process: this.selectedProcess,\n          partGroup: this.selectedPartGroup\n        };\n\n        const response = await fetch('/api-statit2/execute_phase1_dropdown_query', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify(queryParams),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.chartData = data.chart_data || [];\n          this.chartType = data.chart_type || 'bar';\n          this.resultsTitle = data.title || 'Analysis Results';\n          this.aiResponse = '';\n        }\n      } catch (error) {\n        console.error('Error executing dropdown query:', error);\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    async executeAiQuery() {\n      if (this.isLoading || !this.aiPrompt.trim()) return;\n\n      this.isLoading = true;\n      this.hasResults = true;\n\n      try {\n        const token = this.$store.getters.getToken;\n        \n        const response = await fetch('/api-statit2/execute_phase1_ai_query', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n            Authorization: `Bearer ${token}`,\n          },\n          body: JSON.stringify({ \n            prompt: this.aiPrompt.trim()\n          }),\n        });\n\n        const data = await this.handleResponse(response);\n        if (data && data.status_res === 'success') {\n          this.chartData = data.chart_data || [];\n          this.chartType = data.chart_type || 'bar';\n          this.resultsTitle = data.title || 'AI Analysis Results';\n          this.aiResponse = data.ai_response || '';\n        }\n      } catch (error) {\n        console.error('Error executing AI query:', error);\n      } finally {\n        this.isLoading = false;\n      }\n    },\n\n    clearAiPrompt() {\n      this.aiPrompt = '';\n      this.clearResults();\n    },\n\n    clearResults() {\n      this.chartData = [];\n      this.aiResponse = '';\n      this.hasResults = false;\n      this.resultsTitle = '';\n    },\n\n    handleResponse(response) {\n      if (!response.ok) {\n        if (response.status === 401) {\n          // Handle session expiration\n          this.$router.push('/');\n        }\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      return response.json();\n    },\n\n    handleBarClick(data) {\n      console.log('Bar clicked:', data);\n      // Handle bar chart interactions\n    },\n\n    handlePointClick(data) {\n      console.log('Point clicked:', data);\n      // Handle line chart interactions\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../styles/carbon-utils\";\n\n.phase1-grid {\n  min-height: 100vh;\n  background-color: $ui-background;\n}\n\n.page-header {\n  margin-top: $spacing-07;\n  margin-bottom: $spacing-05;\n}\n\n.page-title {\n  @include carbon--type-style('productive-heading-04');\n  color: $text-01;\n  margin-bottom: $spacing-03;\n}\n\n.page-description {\n  @include carbon--type-style('body-long-01');\n  color: $text-02;\n}\n\n.control-panel {\n  margin-bottom: $spacing-05;\n}\n\n.controls-tile {\n  padding: $spacing-05;\n}\n\n.controls-title {\n  @include carbon--type-style('productive-heading-02');\n  color: $text-01;\n  margin-bottom: $spacing-05;\n}\n\n.control-section {\n  margin-bottom: $spacing-05;\n}\n\n.control-label {\n  @include carbon--type-style('label-01');\n  color: $text-01;\n  display: block;\n  margin-bottom: $spacing-03;\n}\n\n.query-method-group {\n  display: flex;\n  gap: $spacing-05;\n}\n\n.dropdown-builder {\n  .filter-row {\n    display: flex;\n    gap: $spacing-05;\n    margin-bottom: $spacing-04;\n    flex-wrap: wrap;\n  }\n\n  .filter-group {\n    flex: 1;\n    min-width: 200px;\n  }\n\n  .filter-label {\n    @include carbon--type-style('label-01');\n    color: $text-01;\n    display: block;\n    margin-bottom: $spacing-02;\n  }\n\n  .filter-dropdown {\n    width: 100%;\n  }\n}\n\n.ai-prompt-section {\n  .prompt-input-group {\n    margin-bottom: $spacing-04;\n  }\n\n  .prompt-textarea {\n    width: 100%;\n  }\n\n  .prompt-controls {\n    display: flex;\n    gap: $spacing-03;\n  }\n}\n\n.execute-button {\n  min-width: 120px;\n}\n\n.clear-button {\n  min-width: 80px;\n}\n\n.results-section {\n  margin-bottom: $spacing-07;\n}\n\n.results-tile {\n  padding: $spacing-05;\n}\n\n.results-title {\n  @include carbon--type-style('productive-heading-02');\n  color: $text-01;\n  margin-bottom: $spacing-05;\n}\n\n.loading-section {\n  text-align: center;\n  padding: $spacing-07;\n  \n  p {\n    @include carbon--type-style('body-long-01');\n    color: $text-02;\n    margin-top: $spacing-03;\n  }\n}\n\n.chart-section {\n  min-height: 400px;\n}\n\n.ai-response-section {\n  margin-top: $spacing-05;\n  padding: $spacing-04;\n  background-color: $ui-01;\n  border-radius: $spacing-02;\n\n  h5 {\n    @include carbon--type-style('productive-heading-01');\n    color: $text-01;\n    margin-bottom: $spacing-03;\n  }\n}\n\n.ai-response-content {\n  @include carbon--type-style('body-long-01');\n  color: $text-01;\n  line-height: 1.6;\n  white-space: pre-wrap;\n}\n\n.no-results {\n  text-align: center;\n  padding: $spacing-07;\n  \n  p {\n    @include carbon--type-style('body-long-01');\n    color: $text-02;\n  }\n}\n</style>\n"]}]}
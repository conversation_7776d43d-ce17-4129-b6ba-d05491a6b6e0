{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\SavedReports\\SavedReports.vue?vue&type=template&id=ffe6e6f6&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\SavedReports\\SavedReports.vue", "mtime": 1756311842621}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
{"items": [{"id": "at-001", "commodity": "Power", "group": "<PERSON>", "pn": "02ED368", "test": "FAB", "deadline": "2024-11-25", "expectedResolution": "2024-11-20", "action": "Bringing parts to FA, expected results Thurs. The team is working on validating the new fan design with improved airflow characteristics.", "expectedImprovements": "30% increase in airflow, 15% reduction in noise levels", "status": "Resolved", "priority": "High", "source": "manual", "progress": 65, "assignee": "Stevana C", "completed": false, "completedDate": null, "dateStarted": "2024-10-15", "monitorDate": null, "resolutionDate": "2024-11-20", "notes": "This is a critical component for the next-gen server cooling system. We need to ensure it meets all thermal requirements under various load conditions.", "actionNotes": [{"date": "2024-11-15", "content": "Initial testing shows promising results. Need to verify under extreme conditions.", "author": "Stevana C"}, {"date": "2024-11-10", "content": "Received parts from supplier. Quality looks good.", "author": "Stevana C"}], "actionItems": [{"title": "Thermal Analysis", "description": "Complete thermal analysis under various load conditions", "completed": true, "completedDate": "2024-11-14", "lastUpdated": "2024-11-14", "notes": [{"date": "2024-11-14", "content": "Analysis completed. Results exceed expectations under all test conditions.", "author": "Stevana C"}, {"date": "2024-11-12", "content": "Started thermal testing with new equipment. Initial results look promising.", "author": "Stevana C"}], "showAddNote": false, "newNote": ""}, {"title": "Stress Testing", "description": "Perform stress testing under extreme conditions", "completed": false, "completedDate": null, "lastUpdated": "2024-11-16", "notes": [{"date": "2024-11-16", "content": "Started stress testing phase. Running 24-hour continuous load test.", "author": "Stevana C"}], "showAddNote": false, "newNote": ""}, {"title": "Documentation", "description": "Complete technical documentation and validation reports", "completed": false, "completedDate": null, "lastUpdated": "2024-11-10", "notes": [], "showAddNote": false, "newNote": ""}], "updates": [{"date": "2024-08-15", "content": "Initial design review completed", "updatedBy": "Stevana C"}, {"date": "2024-09-01", "content": "Prototype testing started", "updatedBy": "Stevana C"}], "createdAt": "2024-08-01", "updatedAt": "2025-06-02", "editableTest": "FAB", "isEditingTest": false, "isEditingDL": false, "isEditingER": false, "issues": []}, {"id": "at-002", "commodity": "Cable", "group": "SMP10", "pn": "02EA657", "test": "FUL", "deadline": "2024-10-15", "expectedResolution": "2024-10-10", "action": "RMA to Amphenol. The cable connectors are showing intermittent connection issues during stress testing.", "expectedImprovements": "Eliminate intermittent connection failures, improve reliability by 99.9%", "status": "Monitored", "priority": "Medium", "source": "pqe", "progress": 40, "assignee": "<PERSON>", "completed": false, "completedDate": null, "dateStarted": "2024-08-05", "monitorDate": "2024-10-15", "resolutionDate": null, "notes": "This is the third batch with similar issues. We may need to consider alternative suppliers if Amphenol cannot resolve the quality issues.", "actionNotes": [{"date": "2024-11-12", "content": "Contacted Amphenol support team. They are investigating the issue.", "author": "<PERSON>"}], "updates": [{"date": "2024-08-10", "content": "Identified connection issues during stress testing", "updatedBy": "<PERSON>"}, {"date": "2024-08-25", "content": "Sent samples to Amphenol for analysis", "updatedBy": "<PERSON>"}], "createdAt": "2024-08-05", "updatedAt": "2024-08-25"}, {"id": "at-003", "commodity": "Memory", "group": "Centaur-D", "pn": "01KM429", "test": "FAB", "deadline": "2024-09-30", "expectedResolution": "2024-09-25", "action": "Completing final validation tests. All performance metrics are within expected ranges.", "expectedImprovements": "Validation of performance metrics, documentation for production approval", "status": "Resolved", "priority": "Low", "source": "manual", "progress": 100, "assignee": "<PERSON>n L", "completed": true, "completedDate": "2024-09-15", "dateStarted": "2024-07-10", "monitorDate": null, "resolutionDate": "2024-09-15", "notes": "This is a standard validation for the new memory modules. No significant issues encountered during testing.", "actionNotes": [{"date": "2024-09-15", "content": "All validation tests completed successfully. Documentation finalized and ready for production.", "author": "<PERSON>n L"}, {"date": "2024-08-15", "content": "Performance metrics are consistently within expected ranges across all test scenarios.", "author": "<PERSON>n L"}], "updates": [{"date": "2024-07-15", "content": "Started validation testing", "updatedBy": "<PERSON>n L"}, {"date": "2024-08-01", "content": "Performance metrics within expected ranges", "updatedBy": "<PERSON>n L"}, {"date": "2024-09-15", "content": "Completed all tests, documentation finalized", "updatedBy": "<PERSON>n L"}], "createdAt": "2024-07-10", "updatedAt": "2024-09-15"}, {"id": "at-004", "commodity": "FAB", "group": "<PERSON><PERSON><PERSON>", "pn": "02CK123", "test": "FAB", "deadline": "2024-12-15", "expectedResolution": "2024-12-10", "action": "Investigating yield issues with the latest batch. Initial analysis suggests a potential issue with the etching process.", "expectedImprovements": "Increase yield from 75% to 90%, reduce defect rate by 50%", "status": "In-Progress", "priority": "High", "source": "pqe", "progress": 35, "assignee": "<PERSON>", "completed": false, "completedDate": null, "dateStarted": "2024-08-15", "monitorDate": null, "resolutionDate": null, "notes": "This is a critical component for upcoming product launches. Need to resolve yield issues quickly to avoid production delays.", "actionNotes": [{"date": "2024-11-16", "content": "Action content example", "author": "<PERSON>"}], "actionItems": [{"title": "Process Optimization", "description": "Action item content example", "completed": true, "completedDate": "2025-06-18", "lastUpdated": "2025-06-18", "notes": [{"date": "2024-11-16", "content": "Action item notes example 1", "author": "<PERSON>"}, {"date": "2024-11-14", "content": "Action item notes example 2", "author": "<PERSON>"}], "showAddNote": false, "newNote": ""}, {"title": "Validation Testing", "description": "Validate optimized process with production-scale testing", "completed": false, "completedDate": null, "lastUpdated": "2025-06-02", "notes": [], "showAddNote": false, "newNote": ""}, {"title": "Root Cause Analysis", "description": "Identify the root cause of yield issues in the etching process", "completed": true, "completedDate": "2024-11-10", "lastUpdated": "2024-11-10", "notes": [{"date": "2024-11-10", "content": "Action item notes example 1", "author": "<PERSON>"}], "showAddNote": false, "newNote": ""}], "updates": [{"date": "2024-08-20", "content": "Identified yield issues in latest batch", "updatedBy": "<PERSON>"}, {"date": "2024-09-05", "content": "Initial analysis points to etching process issues", "updatedBy": "<PERSON>"}, {"date": "2025-06-02", "content": "Action item \"Process Optimization\" marked as completed", "updatedBy": "<PERSON>"}, {"date": "2025-06-02", "content": "Action item \"Validation Testing\" marked as incomplete", "updatedBy": "<PERSON>"}, {"date": "2025-06-02", "content": "Action item \"Validation Testing\" marked as completed", "updatedBy": "<PERSON>"}, {"date": "2025-06-02", "content": "Action item \"Validation Testing\" marked as incomplete", "updatedBy": "<PERSON>"}, {"date": "2025-06-18", "content": "Action item \"Process Optimization\" marked as incomplete", "updatedBy": "Current User"}, {"date": "2025-06-18", "content": "Action item \"Process Optimization\" marked as completed", "updatedBy": "Current User"}, {"date": "2025-06-18", "content": "Action item \"Process Optimization\" marked as incomplete", "updatedBy": "Current User"}, {"date": "2025-06-18", "content": "Action item \"Process Optimization\" marked as completed", "updatedBy": "Current User"}, {"date": "2025-06-18", "content": "Action item \"Process Optimization\" marked as incomplete", "updatedBy": "Current User"}, {"date": "2025-06-18", "content": "Action item \"Process Optimization\" marked as completed", "updatedBy": "Current User"}], "createdAt": "2024-08-15", "updatedAt": "2025-06-18", "editableTest": "FAB", "isEditingTest": true, "isEditingDL": false, "isEditingER": false, "issues": []}, {"id": "at-005", "commodity": "FUL", "group": "Felis <PERSON>", "pn": "01JT456", "test": "FUL", "deadline": "2024-11-30", "expectedResolution": "2024-11-25", "action": "Conducting additional stress tests after recent design changes. Initial results show improved performance under high load conditions.", "expectedImprovements": "20% better thermal performance, eliminate throttling under sustained load", "status": "In-Progress", "priority": "Medium", "source": "manual", "progress": 70, "assignee": "Maria L", "completed": false, "completedDate": null, "dateStarted": "2024-08-01", "monitorDate": null, "resolutionDate": null, "notes": "Design changes were implemented to address previous thermal issues. Need comprehensive validation before mass production.", "actionNotes": [{"date": "2024-11-14", "content": "Thermal performance tests are exceeding expectations. Ready to move to final validation phase.", "author": "Maria L"}], "updates": [{"date": "2024-08-05", "content": "Implemented design changes to address thermal issues", "updatedBy": "Maria L"}, {"date": "2024-08-25", "content": "Initial stress tests show improved performance", "updatedBy": "Maria L"}, {"date": "2024-09-10", "content": "Started comprehensive validation testing", "updatedBy": "Maria L"}], "createdAt": "2024-08-01", "updatedAt": "2024-09-10"}, {"id": "at-006", "commodity": "Power", "group": "Parthenon HLA", "pn": "03BN789", "test": "MST", "deadline": "2024-12-10", "expectedResolution": "2024-12-05", "action": "Finalizing integration tests with new power management firmware. Performance metrics are meeting expectations but need additional endurance testing.", "expectedImprovements": "10% power efficiency improvement, better thermal management", "status": "In-Progress", "progress": 80, "assignee": "<PERSON>", "completed": false, "completedDate": null, "dateStarted": "2024-08-10", "monitorDate": null, "resolutionDate": null, "notes": "This is part of the power efficiency improvement initiative. Need to ensure compatibility with all system configurations.", "actionNotes": [{"date": "2024-11-13", "content": "Endurance testing is progressing well. No compatibility issues found so far with different system configurations.", "author": "<PERSON>"}], "updates": [{"date": "2024-08-15", "content": "Initial firmware integration completed", "updatedBy": "<PERSON>"}, {"date": "2024-09-01", "content": "Performance testing shows 8% efficiency improvement", "updatedBy": "<PERSON>"}, {"date": "2024-09-15", "content": "Started endurance testing, expected to complete in 2 weeks", "updatedBy": "<PERSON>"}], "createdAt": "2024-08-10", "updatedAt": "2024-09-15"}]}
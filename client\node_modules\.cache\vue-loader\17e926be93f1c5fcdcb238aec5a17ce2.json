{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=template&id=2da97105&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756312252514}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=template&id=2da97105&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756316352837}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
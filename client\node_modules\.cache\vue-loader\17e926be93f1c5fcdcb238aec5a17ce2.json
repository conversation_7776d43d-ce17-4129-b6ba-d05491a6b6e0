{"remainingRequest": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue?vue&type=template&id=2da97105&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\src\\views\\Phase1\\Phase1.vue", "mtime": 1756311176776}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\Documents\\statit_2\\client\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}
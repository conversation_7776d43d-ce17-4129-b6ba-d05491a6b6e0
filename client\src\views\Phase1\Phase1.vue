<template>
  <cv-grid class="phase1-grid">
    <MainHeader :expandedSideNav="expandedSideNav" :useFixed="useFixed" @panel-toggle="toggleSideNav" />
    
    <cv-row class="page-header">
      <cv-column>
        <h1 class="page-title">Phase 1 Analysis</h1>
        <p class="page-description">Advanced data analysis with flexible querying capabilities</p>
      </cv-column>
    </cv-row>

    <!-- Control Panel -->
    <cv-row class="control-panel">
      <cv-column :lg="8" :md="6" :sm="4">
        <cv-tile class="controls-tile">
          <div class="controls-header">
            <h4 class="controls-title">Analysis Controls</h4>

            <!-- Query Method Selection -->
            <cv-radio-group
              v-model="queryMethod"
              @change="handleQueryMethodChange"
              class="query-method-group"
            >
              <cv-radio-button
                name="query-method"
                label="Dropdown Builder"
                value="dropdown"
              />
              <cv-radio-button
                name="query-method"
                label="AI Prompt"
                value="ai"
              />
            </cv-radio-group>
          </div>

          <!-- Dropdown Query Builder -->
          <div v-if="queryMethod === 'dropdown'" class="dropdown-builder">
            <div class="filter-row">
              <div class="filter-group">
                <label class="filter-label">View By:</label>
                <cv-dropdown
                  v-model="viewBy"
                  @change="handleViewByChange"
                  class="filter-dropdown"
                >
                  <cv-dropdown-item value="rootCause">Root Cause</cv-dropdown-item>
                  <cv-dropdown-item value="vintage">Vintage</cv-dropdown-item>
                  <cv-dropdown-item value="sector">Sector</cv-dropdown-item>
                  <cv-dropdown-item value="supplier">Supplier</cv-dropdown-item>
                  <cv-dropdown-item value="partNum">Part Number</cv-dropdown-item>
                  <cv-dropdown-item value="category">Category</cv-dropdown-item>
                </cv-dropdown>
              </div>

              <div class="filter-group">
                <label class="filter-label">Time Range:</label>
                <cv-dropdown
                  v-model="timeRange"
                  @change="handleTimeRangeChange"
                  class="filter-dropdown"
                >
                  <cv-dropdown-item value="1month">1 Month</cv-dropdown-item>
                  <cv-dropdown-item value="3month">3 Months</cv-dropdown-item>
                  <cv-dropdown-item value="6month">6 Months</cv-dropdown-item>
                  <cv-dropdown-item value="12month">12 Months</cv-dropdown-item>
                  <cv-dropdown-item value="custom">Custom Range</cv-dropdown-item>
                </cv-dropdown>
              </div>

              <div class="filter-group">
                <label class="filter-label">Process:</label>
                <cv-dropdown
                  v-model="selectedProcess"
                  @change="handleProcessChange"
                  class="filter-dropdown"
                >
                  <cv-dropdown-item value="all">All Processes</cv-dropdown-item>
                  <cv-dropdown-item value="FAB">FAB</cv-dropdown-item>
                  <cv-dropdown-item value="FUL">FUL</cv-dropdown-item>
                </cv-dropdown>
              </div>
            </div>

            <div class="filter-row">
              <div class="filter-group">
                <label class="filter-label">Part Group:</label>
                <cv-dropdown
                  v-model="selectedPartGroup"
                  @change="handlePartGroupChange"
                  class="filter-dropdown"
                  :disabled="!partGroupOptions.length"
                >
                  <cv-dropdown-item value="all">All Part Groups</cv-dropdown-item>
                  <cv-dropdown-item
                    v-for="group in partGroupOptions"
                    :key="group.value"
                    :value="group.value"
                  >
                    {{ group.label }}
                  </cv-dropdown-item>
                </cv-dropdown>
              </div>

              <div class="filter-group" v-if="timeRange === 'custom'">
                <label class="filter-label">Date Range:</label>
                <cv-date-picker
                  v-model="customDateRange"
                  label="Select Date Range"
                  kind="range"
                  :cal-options="calOptions"
                  placeholder="yyyy-mm-dd"
                  @change="handleDateRangeChange"
                />
              </div>

              <div class="filter-group action-group">
                <cv-button
                  @click="executeDropdownQuery"
                  :disabled="isLoading"
                  class="execute-button"
                >
                  {{ isLoading ? 'Loading...' : 'Execute Query' }}
                </cv-button>
              </div>
            </div>
          </div>

          <!-- AI Prompt Interface -->
          <div v-if="queryMethod === 'ai'" class="ai-prompt-section">
            <div class="prompt-input-group">
              <label class="control-label">Natural Language Query:</label>
              <cv-text-area
                v-model="aiPrompt"
                placeholder="Example: Show me the top 5 root causes for FAB process in the last 3 months with failure rates above 2%"
                rows="3"
                class="prompt-textarea"
              />
            </div>
            <div class="prompt-controls">
              <cv-button
                @click="executeAiQuery"
                :disabled="isLoading || !aiPrompt.trim()"
                class="execute-button"
              >
                {{ isLoading ? 'Processing...' : 'Ask AI' }}
              </cv-button>
              <cv-button
                kind="secondary"
                @click="clearAiPrompt"
                class="clear-button"
              >
                Clear
              </cv-button>
            </div>
          </div>
        </cv-tile>
      </cv-column>

      <!-- Action Panel -->
      <cv-column :lg="4" :md="2" :sm="4" v-if="hasResults">
        <cv-tile class="action-tile">
          <h5 class="action-title">Actions</h5>
          <div class="action-buttons">
            <cv-button
              @click="saveJob"
              :disabled="isLoading"
              class="action-button"
              kind="secondary"
            >
              Save Job
            </cv-button>
            <cv-button
              @click="exportData('csv')"
              :disabled="isLoading || !chartData.length"
              class="action-button"
              kind="tertiary"
            >
              Export CSV
            </cv-button>
            <cv-button
              @click="exportData('pdf')"
              :disabled="isLoading || !chartData.length"
              class="action-button"
              kind="tertiary"
            >
              Export PDF
            </cv-button>
          </div>
        </cv-tile>
      </cv-column>
    </cv-row>

    <!-- Results Section -->
    <cv-row class="results-section" v-if="hasResults">
      <cv-column>
        <cv-tile class="results-tile">
          <div class="results-header">
            <h4 class="results-title">{{ resultsTitle }}</h4>
            <div class="results-meta" v-if="!isLoading && chartData.length > 0">
              <span class="data-count">{{ chartData.length }} data points</span>
              <span class="query-time">{{ queryExecutionTime }}</span>
            </div>
          </div>

          <!-- Loading State -->
          <div v-if="isLoading" class="loading-section">
            <cv-loading />
            <p>Processing your query...</p>
          </div>

          <!-- Chart Display -->
          <div v-else-if="chartData.length > 0" class="chart-section">
            <div class="chart-container">
              <BarChart
                v-if="chartType === 'bar'"
                :data="chartData"
                @bar-clicked="handleBarClick"
                :loading="false"
                class="chart-component"
              />
              <LineChart
                v-if="chartType === 'line'"
                :data="chartData"
                @point-clicked="handlePointClick"
                :loading="false"
                class="chart-component"
              />
            </div>

            <!-- Data Table -->
            <div class="data-table-section">
              <h5>Data Summary</h5>
              <cv-data-table
                :columns="tableColumns"
                :data="tableData"
                :pagination="{ numberOfItems: chartData.length }"
                class="results-table"
              >
                <template v-slot:cell="{ cell }">
                  <div v-if="cell.header === 'value'" class="value-cell">
                    {{ formatValue(cell.value) }}
                  </div>
                  <div v-else>
                    {{ cell.value }}
                  </div>
                </template>
              </cv-data-table>
            </div>
          </div>

          <!-- AI Response Display -->
          <div v-if="aiResponse" class="ai-response-section">
            <h5>AI Analysis:</h5>
            <div class="ai-response-content">
              {{ aiResponse }}
            </div>
          </div>

          <!-- No Results Message -->
          <div v-else-if="!isLoading" class="no-results">
            <p>No data found for the current query. Try adjusting your filters or prompt.</p>
          </div>
        </cv-tile>
      </cv-column>
    </cv-row>
  </cv-grid>
</template>

<script>
import MainHeader from '../../components/MainHeader';
import BarChart from '../../components/BarChart';
import LineChart from '../../components/LineChart';

export default {
  name: 'Phase1Page',
  components: {
    MainHeader,
    BarChart,
    LineChart
  },
  data() {
    return {
      // UI State
      expandedSideNav: false,
      useFixed: true,
      isLoading: false,
      hasResults: false,

      // Query Method
      queryMethod: 'dropdown',

      // Dropdown Query Builder
      viewBy: 'rootCause',
      timeRange: '3month',
      customDateRange: '',
      selectedProcess: 'all',
      selectedPartGroup: 'all',
      partGroupOptions: [],

      // AI Prompt
      aiPrompt: '',
      aiResponse: '',

      // Results
      chartData: [],
      chartType: 'bar',
      resultsTitle: '',
      queryExecutionTime: '',

      // Table Data
      tableColumns: [
        { header: 'Category', key: 'group' },
        { header: 'Value', key: 'value' },
        { header: 'Details', key: 'details' }
      ],

      // Current Job Data
      currentJobData: null,

      // Options
      calOptions: { dateFormat: "Y-m-d" }
    };
  },
  computed: {
    tableData: function() {
      var self = this;
      return self.chartData.map(function(item, index) {
        return {
          id: index,
          group: item.group,
          value: item.value,
          details: item.details || 'N/A'
        };
      });
    }
  },
  mounted() {
    this.loadPartGroupOptions();
  },
  methods: {
    toggleSideNav: function() {
      this.expandedSideNav = !this.expandedSideNav;
    },

    handleQueryMethodChange: function() {
      this.clearResults();
    },

    handleViewByChange: function() {
      // Auto-execute if we have enough parameters
      if (this.queryMethod === 'dropdown') {
        this.executeDropdownQuery();
      }
    },

    handleTimeRangeChange: function() {
      if (this.queryMethod === 'dropdown') {
        this.executeDropdownQuery();
      }
    },

    handleDateRangeChange: function() {
      if (this.queryMethod === 'dropdown') {
        this.executeDropdownQuery();
      }
    },

    handleProcessChange: function() {
      this.loadPartGroupOptions();
      if (this.queryMethod === 'dropdown') {
        this.executeDropdownQuery();
      }
    },

    handlePartGroupChange: function() {
      if (this.queryMethod === 'dropdown') {
        this.executeDropdownQuery();
      }
    },

    loadPartGroupOptions: function() {
      var self = this;
      var token = self.$store.getters.getToken;

      fetch('/api-statit2/get_phase1_part_groups', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
        body: JSON.stringify({
          process: self.selectedProcess
        }),
      })
      .then(function(response) {
        return self.handleResponse(response);
      })
      .then(function(data) {
        if (data && data.status_res === 'success') {
          self.partGroupOptions = data.part_groups || [];
        }
      })
      .catch(function(error) {
        console.error('Error loading part group options:', error);
      });
    },

    executeDropdownQuery: function() {
      var self = this;
      if (self.isLoading) return;

      self.isLoading = true;
      self.hasResults = true;
      var startTime = Date.now();

      var token = self.$store.getters.getToken;
      var queryParams = {
        viewBy: self.viewBy,
        timeRange: self.timeRange,
        customDateRange: self.timeRange === 'custom' ? self.customDateRange : null,
        process: self.selectedProcess,
        partGroup: self.selectedPartGroup
      };

      fetch('/api-statit2/execute_phase1_dropdown_query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
        body: JSON.stringify(queryParams),
      })
      .then(function(response) {
        return self.handleResponse(response);
      })
      .then(function(data) {
        if (data && data.status_res === 'success') {
          self.chartData = data.chart_data || [];
          self.chartType = data.chart_type || 'bar';
          self.resultsTitle = data.title || 'Analysis Results';
          self.aiResponse = '';

          // Store current job data
          self.currentJobData = {
            type: 'dropdown',
            params: queryParams,
            results: data,
            timestamp: new Date().toISOString()
          };
        }
      })
      .catch(function(error) {
        console.error('Error executing dropdown query:', error);
      })
      .finally(function() {
        self.isLoading = false;
        var endTime = Date.now();
        self.queryExecutionTime = 'Executed in ' + (endTime - startTime) + 'ms';
      });
    },

    executeAiQuery: function() {
      var self = this;
      if (self.isLoading || !self.aiPrompt.trim()) return;

      self.isLoading = true;
      self.hasResults = true;
      var startTime = Date.now();

      var token = self.$store.getters.getToken;

      fetch('/api-statit2/execute_phase1_ai_query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
        body: JSON.stringify({
          prompt: self.aiPrompt.trim()
        }),
      })
      .then(function(response) {
        return self.handleResponse(response);
      })
      .then(function(data) {
        if (data && data.status_res === 'success') {
          self.chartData = data.chart_data || [];
          self.chartType = data.chart_type || 'bar';
          self.resultsTitle = data.title || 'AI Analysis Results';
          self.aiResponse = data.ai_response || '';

          // Store current job data
          self.currentJobData = {
            type: 'ai',
            params: { prompt: self.aiPrompt.trim() },
            results: data,
            timestamp: new Date().toISOString()
          };
        }
      })
      .catch(function(error) {
        console.error('Error executing AI query:', error);
      })
      .finally(function() {
        self.isLoading = false;
        var endTime = Date.now();
        self.queryExecutionTime = 'Executed in ' + (endTime - startTime) + 'ms';
      });
    },

    clearAiPrompt: function() {
      this.aiPrompt = '';
      this.clearResults();
    },

    clearResults: function() {
      this.chartData = [];
      this.aiResponse = '';
      this.hasResults = false;
      this.resultsTitle = '';
    },

    handleResponse: function(response) {
      if (!response.ok) {
        if (response.status === 401) {
          // Handle session expiration
          this.$router.push('/');
        }
        throw new Error('HTTP error! status: ' + response.status);
      }
      return response.json();
    },

    handleBarClick: function(data) {
      console.log('Bar clicked:', data);
      // Handle bar chart interactions
    },

    handlePointClick: function(data) {
      console.log('Point clicked:', data);
      // Handle line chart interactions
    },

    formatValue: function(value) {
      if (typeof value === 'number') {
        return value.toLocaleString();
      }
      return value;
    },

    exportData: function(format) {
      var self = this;
      if (!self.chartData.length) return;

      var token = self.$store.getters.getToken;
      var exportData = {
        format: format,
        data: self.chartData,
        title: self.resultsTitle,
        queryParams: (self.currentJobData && self.currentJobData.params) || {},
        timestamp: new Date().toISOString()
      };

      fetch('/api-statit2/export_phase1_data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
        body: JSON.stringify(exportData),
      })
      .then(function(response) {
        if (response.ok) {
          return response.blob();
        }
        throw new Error('Export failed');
      })
      .then(function(blob) {
        var url = window.URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = 'phase1_analysis_' + Date.now() + '.' + format;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      })
      .catch(function(error) {
        console.error('Error exporting data:', error);
      });
    },

    saveJob: function() {
      var self = this;
      if (!self.currentJobData) return;

      var jobName = prompt('Enter a name for this job:');
      if (!jobName) return;

      var token = self.$store.getters.getToken;
      var saveData = {
        name: jobName,
        jobData: self.currentJobData,
        createdAt: new Date().toISOString()
      };

      fetch('/api-statit2/save_phase1_job', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: 'Bearer ' + token,
        },
        body: JSON.stringify(saveData),
      })
      .then(function(response) {
        return self.handleResponse(response);
      })
      .then(function(data) {
        if (data && data.status_res === 'success') {
          alert('Job "' + jobName + '" saved successfully!');
          // Navigate to saved reports tab
          self.$router.push('/saved-reports');
        }
      })
      .catch(function(error) {
        console.error('Error saving job:', error);
        alert('Error saving job. Please try again.');
      });
    }
  }
};
</script>

<style scoped lang="scss">
@import "../../styles/carbon-utils";

.phase1-grid {
  min-height: 100vh;
  background-color: #161616;
  padding: 0 $spacing-05;
}

.page-header {
  margin-top: $spacing-05;
  margin-bottom: $spacing-04;
}

.page-title {
  @include carbon--type-style('productive-heading-03');
  color: $text-01;
  margin-bottom: $spacing-02;
}

.page-description {
  @include carbon--type-style('body-short-01');
  color: $text-02;
}

.control-panel {
  margin-bottom: $spacing-04;
}

.controls-tile {
  padding: $spacing-04;
  height: fit-content;
  background-color: #262626;
}

.controls-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-04;
}

.controls-title {
  @include carbon--type-style('productive-heading-01');
  color: $text-01;
  margin: 0;
}

.control-label {
  @include carbon--type-style('label-01');
  color: $text-01;
  display: block;
  margin-bottom: $spacing-02;
}

.query-method-group {
  display: flex;
  gap: $spacing-04;
}

.action-tile {
  padding: $spacing-04;
  height: fit-content;
  background-color: #262626;
}

.action-title {
  @include carbon--type-style('productive-heading-01');
  color: $text-01;
  margin-bottom: $spacing-03;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: $spacing-03;
}

.action-button {
  width: 100%;
}

.dropdown-builder {
  .filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: $spacing-04;
    margin-bottom: $spacing-03;
  }

  .filter-group {
    min-width: 0;

    &.action-group {
      display: flex;
      align-items: end;
    }
  }

  .filter-label {
    @include carbon--type-style('label-01');
    color: $text-01;
    display: block;
    margin-bottom: $spacing-02;
  }

  .filter-dropdown {
    width: 100%;
  }
}

.ai-prompt-section {
  .prompt-input-group {
    margin-bottom: $spacing-03;
  }

  .prompt-textarea {
    width: 100%;
  }

  .prompt-controls {
    display: flex;
    gap: $spacing-03;
  }
}

.execute-button {
  min-width: 120px;
}

.clear-button {
  min-width: 80px;
}

.results-section {
  margin-bottom: $spacing-05;
}

.results-tile {
  padding: $spacing-04;
  background-color: #262626;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-04;
}

.results-title {
  @include carbon--type-style('productive-heading-02');
  color: $text-01;
  margin: 0;
}

.results-meta {
  display: flex;
  gap: $spacing-04;

  .data-count, .query-time {
    @include carbon--type-style('caption-01');
    color: $text-02;
  }
}

.loading-section {
  text-align: center;
  padding: $spacing-05;

  p {
    @include carbon--type-style('body-short-01');
    color: $text-02;
    margin-top: $spacing-03;
  }
}

.chart-section {
  .chart-container {
    height: 350px;
    margin-bottom: $spacing-05;
  }

  .chart-component {
    height: 100%;
  }
}

.data-table-section {
  h5 {
    @include carbon--type-style('productive-heading-01');
    color: $text-01;
    margin-bottom: $spacing-03;
  }

  .results-table {
    max-height: 300px;
    overflow-y: auto;
  }

  .value-cell {
    font-weight: 600;
    color: $text-01;
  }
}

.ai-response-section {
  margin-top: $spacing-04;
  padding: $spacing-04;
  background-color: $ui-01;
  border-radius: $spacing-02;

  h5 {
    @include carbon--type-style('productive-heading-01');
    color: $text-01;
    margin-bottom: $spacing-03;
  }
}

.ai-response-content {
  @include carbon--type-style('body-short-01');
  color: $text-01;
  line-height: 1.5;
  white-space: pre-wrap;
}

.no-results {
  text-align: center;
  padding: $spacing-05;

  p {
    @include carbon--type-style('body-short-01');
    color: $text-02;
  }
}
</style>
